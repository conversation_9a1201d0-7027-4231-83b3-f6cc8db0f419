<aside class="left-sidebar">
    <?php if($upSellProducts->isNotEmpty()): ?>
        <div class="vertical-products">
            <div class="vertical-products-header">
                <h4 class="section-title"><?php echo e(trans('storefront::product.you_might_also_like')); ?></h4>
            </div>

            <div class="vertical-products-slider swiper" x-ref="upSellProducts">
                <div x-cloak class="swiper-wrapper">
                    <?php $__currentLoopData = $upSellProducts->chunk(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $upSellProductChunks): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <div class="vertical-products-slide">
                                <?php $__currentLoopData = $upSellProductChunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $upSellProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div x-data="ProductCard(<?php echo e($upSellProduct); ?>)" class="vertical-product-card">
                                        <a :href="productUrl" class="product-image">
                                            <img
                                                :src="baseImage"
                                                :class="{ 'image-placeholder': !hasBaseImage }"
                                                :alt="productName"
                                                loading="lazy"
                                            />

                                            <div class="product-image-layer"></div>
                                        </a>

                                        <div class="product-info">
                                            <a :href="productUrl" class="product-name">
                                                <h6 x-text="productName"></h6>
                                            </a>

                                            <?php echo $__env->make('storefront::public.partials.product_rating', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        
                                            <div class="product-price" x-html="productPrice"></div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    <?php endif; ?>

    <?php if($banner->image->exists): ?>
        <a
            href="<?php echo e($banner->call_to_action_url); ?>"
            class="banner d-none d-lg-block"
            target="<?php echo e($banner->open_in_new_window ? '_blank' : '_self'); ?>"
        >
            <img src="<?php echo e($banner->image->path); ?>" alt="Banner" loading="lazy" />
        </a>
    <?php endif; ?>
</aside>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/left_sidebar.blade.php ENDPATH**/ ?>