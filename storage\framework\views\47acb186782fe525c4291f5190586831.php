<?php $__env->startComponent('admin::components.page.header'); ?>
    <?php $__env->slot('title', trans('support::sitemap.sitemap')); ?>

    <li class="active"><?php echo e(trans('support::sitemap.sitemap')); ?></li>
<?php echo $__env->renderComponent(); ?>

<?php $__env->startSection('content'); ?>
    <form method="POST" action="<?php echo e(route('admin.sitemaps.store')); ?>" enctype="multipart/form-data"
          class="form-horizontal">
        <?php echo csrf_field(); ?>

        <div class="accordion-content">
            <div class="accordion-box-content clearfix">
                <div class="col-md-12">
                    <div class="accordion-box-content">
                        <div class="tab-content clearfix">
                            <div class="tab-pane fade in active">
                                <h4 class="tab-content-title">
                                    <?php echo e(trans('support::sitemap.generate_sitemap')); ?>

                                </h4>

                                <div class="row btn-generate-sitemap">
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary" data-loading>
                                            <?php echo e(trans('support::sitemap.generate')); ?>

                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin::layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Support/Resources/views/admin/sitemap/index.blade.php ENDPATH**/ ?>