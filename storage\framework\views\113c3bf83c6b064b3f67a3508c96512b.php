<script>
    window.FleetCart = {
        version: '<?php echo e(fleetcart_version()); ?>',
        csrfToken: '<?php echo e(csrf_token()); ?>',
        baseUrl: '<?php echo e(url('/')); ?>',
        rtl: <?php echo e(is_rtl() ? 'true' : 'false'); ?>,
        langs: {},
        data: {},
        errors: {},
        selectize: [],
        defaultCurrencySymbol: '<?php echo e(currency_symbol(setting("default_currency"))); ?>'
    };

    FleetCart.langs['admin::admin.buttons.delete'] = '<?php echo e(trans('admin::admin.buttons.delete')); ?>';
    FleetCart.langs['media::media.file_manager.title'] = '<?php echo e(trans('media::media.file_manager.title')); ?>';
    FleetCart.langs['admin::admin.table.search_here'] = '<?php echo e(trans('admin::admin.table.search_here')); ?>';
    FleetCart.langs['admin::admin.table.showing_start_end_total_entries'] = '<?php echo e(trans('admin::admin.table.showing_start_end_total_entries')); ?>';
    FleetCart.langs['admin::admin.table.showing_empty_entries'] = '<?php echo e(trans('admin::admin.table.showing_empty_entries')); ?>';
    FleetCart.langs['admin::admin.table.show_menu_entries'] = '<?php echo e(trans('admin::admin.table.show_menu_entries')); ?>';
    FleetCart.langs['admin::admin.table.filtered_from_max_total_entries'] = '<?php echo e(trans('admin::admin.table.filtered_from_max_total_entries')); ?>';
    FleetCart.langs['admin::admin.table.no_data_available_table'] = '<?php echo e(trans('admin::admin.table.no_data_available_table')); ?>';
    FleetCart.langs['admin::admin.table.loading'] = '<?php echo e(trans('admin::admin.table.loading')); ?>';
    FleetCart.langs['admin::admin.table.no_matching_records_found'] = '<?php echo e(trans('admin::admin.table.no_matching_records_found')); ?>';
</script>

<?php echo $__env->yieldPushContent('globals'); ?>

<?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Admin/Resources/views/partials/globals.blade.php ENDPATH**/ ?>