a:7:{s:6:"_token";s:40:"lrWWxcD83m6Lw9KF5gtcXkaC9BrqPFHMQRFhUuU9";s:6:"locale";s:5:"ar_PS";s:9:"_previous";a:1:{s:3:"url";s:22:"http://cars.test/ar_PS";}s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:67:"OQOhO2MK9NJJ47m95bUpxlqXABMIeweDbDDpBYzd_recently_viewed_cart_items";O:32:"Darryldecode\Cart\CartCollection":2:{s:8:" * items";a:1:{i:23;O:32:"Darryldecode\Cart\ItemCollection":3:{s:8:" * items";a:6:{s:2:"id";i:23;s:4:"name";s:39:"ارضية صندوق خلفي مخصص";s:5:"price";d:130;s:8:"quantity";i:1;s:10:"attributes";O:41:"Darryldecode\Cart\ItemAttributeCollection":2:{s:8:" * items";a:1:{s:7:"product";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:22:{s:2:"id";i:23;s:8:"brand_id";N;s:12:"tax_class_id";N;s:4:"slug";s:22:"ardy-sndok-khlfy-mkhss";s:5:"price";s:8:"130.0000";s:13:"special_price";N;s:18:"special_price_type";s:5:"fixed";s:19:"special_price_start";N;s:17:"special_price_end";N;s:13:"selling_price";s:8:"130.0000";s:3:"sku";N;s:12:"manage_stock";i:0;s:3:"qty";N;s:8:"in_stock";i:1;s:6:"viewed";i:16;s:9:"is_active";i:1;s:8:"new_from";N;s:6:"new_to";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";s:10:"is_virtual";i:0;}s:11:" * original";a:22:{s:2:"id";i:23;s:8:"brand_id";N;s:12:"tax_class_id";N;s:4:"slug";s:22:"ardy-sndok-khlfy-mkhss";s:5:"price";s:8:"130.0000";s:13:"special_price";N;s:18:"special_price_type";s:5:"fixed";s:19:"special_price_start";N;s:17:"special_price_end";N;s:13:"selling_price";s:8:"130.0000";s:3:"sku";N;s:12:"manage_stock";i:0;s:3:"qty";N;s:8:"in_stock";i:1;s:6:"viewed";i:16;s:9:"is_active";i:1;s:8:"new_from";N;s:6:"new_to";N;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";s:10:"is_virtual";i:0;}s:10:" * changes";a:1:{s:6:"viewed";i:16;}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:15:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";i:13;s:16:"is_in_flash_sale";i:14;s:19:"flash_sale_end_date";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:11:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:43:"Modules\Product\Entities\ProductTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:20:"product_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:23;s:10:"product_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:39:"ارضية صندوق خلفي مخصص";s:11:"description";s:1022:"<p data-start="163" data-end="329">أرضية 3D أصلية بتصميم عملي ومتين، توفر حماية شاملة لأرضية السيارة مع مظهر داخلي أنيق.<br data-start="248" data-end="251" />مصنوعة من خامات عالية الجودة، مرنة وسهلة التنظيف، وتأتي بكفالة لـ5 سنوات.</p>
<ul data-start="331" data-end="500">
<li data-start="331" data-end="348">
<p data-start="333" data-end="348">تصميم 3D حوض&nbsp;</p>
</li>
<li data-start="349" data-end="379">
<p data-start="351" data-end="379">جودة عالية مع حماية ممتازة</p>
</li>
<li data-start="380" data-end="403">
<p data-start="382" data-end="403">قابلة للثني والغسيل</p>
</li>
<li data-start="404" data-end="436">
<p data-start="406" data-end="436">تمنح السيارة مظهر داخلي أنيق</p>
</li>
<li data-start="437" data-end="477">
<p data-start="439" data-end="477">مخصصة حسب نوع السيارة</p>
</li>
</ul>";s:17:"short_description";N;}s:11:" * original";a:6:{s:2:"id";i:23;s:10:"product_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:39:"ارضية صندوق خلفي مخصص";s:11:"description";s:1022:"<p data-start="163" data-end="329">أرضية 3D أصلية بتصميم عملي ومتين، توفر حماية شاملة لأرضية السيارة مع مظهر داخلي أنيق.<br data-start="248" data-end="251" />مصنوعة من خامات عالية الجودة، مرنة وسهلة التنظيف، وتأتي بكفالة لـ5 سنوات.</p>
<ul data-start="331" data-end="500">
<li data-start="331" data-end="348">
<p data-start="333" data-end="348">تصميم 3D حوض&nbsp;</p>
</li>
<li data-start="349" data-end="379">
<p data-start="351" data-end="379">جودة عالية مع حماية ممتازة</p>
</li>
<li data-start="380" data-end="403">
<p data-start="382" data-end="403">قابلة للثني والغسيل</p>
</li>
<li data-start="404" data-end="436">
<p data-start="406" data-end="436">تمنح السيارة مظهر داخلي أنيق</p>
</li>
<li data-start="437" data-end="477">
<p data-start="439" data-end="477">مخصصة حسب نوع السيارة</p>
</li>
</ul>";s:17:"short_description";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:10:"variations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:8:"variants";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:10:"categories";O:26:"TypiCMS\NestableCollection":8:{s:8:" * items";a:1:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:1:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:23;s:9:"parent_id";i:19;s:4:"slug";s:23:"ardy-sndok-khlfy-standr";s:8:"position";i:2;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:32:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:10:{s:2:"id";i:23;s:9:"parent_id";i:19;s:4:"slug";s:23:"ardy-sndok-khlfy-standr";s:8:"position";i:2;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:32:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:16:"pivot_product_id";i:23;s:17:"pivot_category_id";i:23;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";N;s:8:" * table";s:18:"product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:23;s:11:"category_id";i:23;}s:11:" * original";a:2:{s:10:"product_id";i:23;s:11:"category_id";i:23;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:17:{i:0;s:8:"brand_id";i:1;s:12:"tax_class_id";i:2;s:4:"slug";i:3;s:3:"sku";i:4;s:5:"price";i:5;s:13:"special_price";i:6;s:18:"special_price_type";i:7;s:19:"special_price_start";i:8;s:17:"special_price_end";i:9;s:13:"selling_price";i:10;s:12:"manage_stock";i:11;s:3:"qty";i:12;s:8:"in_stock";i:13;s:10:"is_virtual";i:14;s:9:"is_active";i:15;s:8:"new_from";i:16;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:11:"category_id";}s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:23;s:11:"category_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:30:"ارضية صندوق خلفي";}s:11:" * original";a:4:{s:2:"id";i:23;s:11:"category_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:30:"ارضية صندوق خلفي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:10:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";s:16:"pivot_product_id";i:23;s:17:"pivot_category_id";i:19;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";N;s:8:" * table";s:18:"product_categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:23;s:11:"category_id";i:19;}s:11:" * original";a:2:{s:10:"product_id";i:23;s:11:"category_id";i:19;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:279;s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:11:"category_id";}s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:11:" * original";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;s:8:" * total";i:2;s:15:" * parentColumn";s:9:"parent_id";s:33:" * removeItemsWithMissingAncestor";b:1;s:14:" * indentChars";s:8:"    ";s:15:" * childrenName";s:5:"items";s:17:" * parentRelation";s:6:"parent";}s:4:"tags";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:10:"attributes";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:30:"Modules\Option\Entities\Option":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:2:{i:0;s:12:"translations";i:1;s:6:"values";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:5;s:4:"type";s:5:"field";s:11:"is_required";i:1;s:9:"is_global";i:0;s:8:"position";i:1;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:10:{s:2:"id";i:5;s:4:"type";s:5:"field";s:11:"is_required";i:1;s:9:"is_global";i:0;s:8:"position";i:1;s:10:"deleted_at";N;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";s:16:"pivot_product_id";i:23;s:15:"pivot_option_id";i:5;}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:11:"is_required";s:7:"boolean";s:9:"is_global";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:3:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";N;s:8:" * table";s:15:"product_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:23;s:9:"option_id";i:5;}s:11:" * original";a:2:{s:10:"product_id";i:23;s:9:"option_id";i:5;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:17:{i:0;s:8:"brand_id";i:1;s:12:"tax_class_id";i:2;s:4:"slug";i:3;s:3:"sku";i:4;s:5:"price";i:5;s:13:"special_price";i:6;s:18:"special_price_type";i:7;s:19:"special_price_start";i:8;s:17:"special_price_end";i:9;s:13:"selling_price";i:10;s:12:"manage_stock";i:11;s:3:"qty";i:12;s:8:"in_stock";i:13;s:10:"is_virtual";i:14;s:9:"is_active";i:15;s:8:"new_from";i:16;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:9:"option_id";}s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Option\Entities\OptionTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:19:"option_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:5;s:9:"option_id";i:5;s:6:"locale";s:5:"ar_PS";s:4:"name";s:41:"نوع السيارة مع الموديل";}s:11:" * original";a:4:{s:2:"id";i:5;s:9:"option_id";i:5;s:6:"locale";s:5:"ar_PS";s:4:"name";s:41:"نوع السيارة مع الموديل";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:6:"values";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:35:"Modules\Option\Entities\OptionValue":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"option_values";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:12;s:9:"option_id";i:5;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:1;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:7:{s:2:"id";i:12;s:9:"option_id";i:5;s:5:"price";N;s:10:"price_type";s:5:"fixed";s:8:"position";i:1;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:5:"price";i:1;s:10:"price_type";i:2;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:5:"label";}s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:6:"option";i:1;s:4:"type";i:2;s:11:"is_required";i:3;s:9:"is_global";i:4;s:8:"position";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:127;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_5527.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/m9gGAMpn3PW5GO7fRoULOvn2jSKh5nivuGOXuj0G.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"266328";s:10:"created_at";s:19:"2025-06-08 17:03:06";s:10:"updated_at";s:19:"2025-06-08 17:03:06";}s:11:" * original";a:17:{s:2:"id";i:127;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_5527.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/m9gGAMpn3PW5GO7fRoULOvn2jSKh5nivuGOXuj0G.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"266328";s:10:"created_at";s:19:"2025-06-08 17:03:06";s:10:"updated_at";s:19:"2025-06-08 17:03:06";s:15:"pivot_entity_id";i:23;s:13:"pivot_file_id";i:127;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:693;s:10:"pivot_zone";s:10:"base_image";s:16:"pivot_created_at";s:19:"2025-06-08 17:08:16";s:16:"pivot_updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:127;s:2:"id";i:693;s:4:"zone";s:10:"base_image";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:127;s:2:"id";i:693;s:4:"zone";s:10:"base_image";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:32:"Modules\Product\Entities\Product":35:{s:13:" * connection";N;s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:9:{s:10:"is_virtual";s:7:"boolean";s:9:"is_active";s:7:"boolean";s:19:"special_price_start";s:8:"datetime";s:17:"special_price_end";s:8:"datetime";s:8:"new_from";s:8:"datetime";s:6:"new_to";s:8:"datetime";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:13:{i:0;s:10:"base_image";i:1;s:17:"additional_images";i:2;s:5:"media";i:3;s:15:"formatted_price";i:4;s:21:"formatted_price_range";i:5;s:28:"has_percentage_special_price";i:6;s:21:"special_price_percent";i:7;s:14:"rating_percent";i:8;s:17:"does_manage_stock";i:9;s:11:"is_in_stock";i:10;s:15:"is_out_of_stock";i:11;s:6:"is_new";i:12;s:7:"variant";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:17:{i:0;s:8:"brand_id";i:1;s:12:"tax_class_id";i:2;s:4:"slug";i:3;s:3:"sku";i:4;s:5:"price";i:5;s:13:"special_price";i:6;s:18:"special_price_type";i:7;s:19:"special_price_start";i:8;s:17:"special_price_end";i:9;s:13:"selling_price";i:10;s:12:"manage_stock";i:11;s:3:"qty";i:12;s:8:"in_stock";i:13;s:10:"is_virtual";i:14;s:9:"is_active";i:15;s:8:"new_from";i:16;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:129;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0082.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/76Xo6nqlf0IUaVAAZyjKLvqI3BAER8znAw9zwwDt.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"122072";s:10:"created_at";s:19:"2025-06-08 17:03:07";s:10:"updated_at";s:19:"2025-06-08 17:03:07";}s:11:" * original";a:17:{s:2:"id";i:129;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0082.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/76Xo6nqlf0IUaVAAZyjKLvqI3BAER8znAw9zwwDt.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"122072";s:10:"created_at";s:19:"2025-06-08 17:03:07";s:10:"updated_at";s:19:"2025-06-08 17:03:07";s:15:"pivot_entity_id";i:23;s:13:"pivot_file_id";i:129;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:694;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-08 17:08:16";s:16:"pivot_updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:129;s:2:"id";i:694;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:129;s:2:"id";i:694;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:944;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:2;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:128;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0086.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/jcLH2w8V3fb0oBknRS1mTVYWFyb9PHgFtIhLet4b.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"137892";s:10:"created_at";s:19:"2025-06-08 17:03:07";s:10:"updated_at";s:19:"2025-06-08 17:03:07";}s:11:" * original";a:17:{s:2:"id";i:128;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0086.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/jcLH2w8V3fb0oBknRS1mTVYWFyb9PHgFtIhLet4b.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"137892";s:10:"created_at";s:19:"2025-06-08 17:03:07";s:10:"updated_at";s:19:"2025-06-08 17:03:07";s:15:"pivot_entity_id";i:23;s:13:"pivot_file_id";i:128;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:695;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-08 17:08:16";s:16:"pivot_updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:128;s:2:"id";i:695;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:128;s:2:"id";i:695;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:944;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:3;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:122;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0087.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/dF3Ne6Ro2b0vTaAIwCJ1sgYW7TUJcYfzmPUZCuWv.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"349585";s:10:"created_at";s:19:"2025-06-08 17:03:05";s:10:"updated_at";s:19:"2025-06-08 17:03:05";}s:11:" * original";a:17:{s:2:"id";i:122;s:7:"user_id";i:1;s:8:"filename";s:12:"IMG_0087.JPG";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/dF3Ne6Ro2b0vTaAIwCJ1sgYW7TUJcYfzmPUZCuWv.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:6:"349585";s:10:"created_at";s:19:"2025-06-08 17:03:05";s:10:"updated_at";s:19:"2025-06-08 17:03:05";s:15:"pivot_entity_id";i:23;s:13:"pivot_file_id";i:122;s:17:"pivot_entity_type";s:32:"Modules\Product\Entities\Product";s:8:"pivot_id";i:696;s:10:"pivot_zone";s:17:"additional_images";s:16:"pivot_created_at";s:19:"2025-06-08 17:08:16";s:16:"pivot_updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:122;s:2:"id";i:696;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:7:{s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:7:"file_id";i:122;s:2:"id";i:696;s:4:"zone";s:17:"additional_images";s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:944;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:32:"Modules\Product\Entities\Product";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}s:7:"reviews";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:5:"brand";O:28:"Modules\Brand\Entities\Brand":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:6:"brands";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"slug";i:1;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"meta";O:30:"Modules\Meta\Entities\MetaData":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"meta_data";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:31;s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:11:" * original";a:5:{s:2:"id";i:31;s:11:"entity_type";s:32:"Modules\Product\Entities\Product";s:9:"entity_id";i:23;s:10:"created_at";s:19:"2025-06-08 17:08:16";s:10:"updated_at";s:19:"2025-06-08 17:08:16";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:9:"entity_id";i:1;s:11:"entity_type";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:2:{i:0;s:10:"meta_title";i:1;s:16:"meta_description";}s:16:" * defaultLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:17:{i:0;s:8:"brand_id";i:1;s:12:"tax_class_id";i:2;s:4:"slug";i:3;s:3:"sku";i:4;s:5:"price";i:5;s:13:"special_price";i:6;s:18:"special_price_type";i:7;s:19:"special_price_start";i:8;s:17:"special_price_end";i:9;s:13:"selling_price";i:10;s:12:"manage_stock";i:11;s:3:"qty";i:12;s:8:"in_stock";i:13;s:10:"is_virtual";i:14;s:9:"is_active";i:15;s:8:"new_from";i:16;s:6:"new_to";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:17:"short_description";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;s:16:" * scoutMetadata";a:0:{}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:10:"conditions";a:0:{}}s:28:" * escapeWhenCastingToString";b:0;s:9:" * config";a:6:{s:14:"format_numbers";b:0;s:8:"decimals";i:0;s:9:"dec_point";s:1:".";s:13:"thousands_sep";s:1:",";s:7:"storage";N;s:6:"events";N;}}}s:28:" * escapeWhenCastingToString";b:0;}s:3:"url";a:0:{}s:14:"fleetcart_auth";s:32:"5xqZ7rcFzCFWdT6p4Y7kolSR0JUwaviS";}