<section x-data="HomeFeatures" class="features-wrap">
    <div class="container">
        <div class="features swiper overflow-hidden">
            <div class="feature-list swiper-wrapper" x-ref="featureList">
                <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="single-feature swiper-slide">
                        <div class="feature-icon">
                            <i class="<?php echo e($feature->icon); ?>"></i>
                        </div>

                        <div class="feature-details">
                            <h6><?php echo e($feature->title); ?></h6>
                            
                            <span><?php echo e($feature->subtitle); ?></span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
        </div>
    </div>
</section>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/home/<USER>/home_features.blade.php ENDPATH**/ ?>