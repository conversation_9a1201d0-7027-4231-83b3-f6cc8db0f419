<div class="row">
    <div class="col-md-8">
        <?php echo e(Form::checkbox('pwa_enabled', trans('setting::attributes.pwa_enabled'), trans('setting::settings.form.enable_pwa'), $errors, $settings)); ?>

        
        <div class="media-picker-divider"></div>

        <?php echo $__env->make('media::admin.image_picker.single', [
            'title' => trans('setting::settings.form.pwa_icon'),
            'inputName' => 'pwa_icon',
            'file' => $icon,
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="media-picker-divider"></div>
        
        <?php echo e(Form::color('pwa_theme_color', trans('setting::attributes.pwa_theme_color'), $errors, $settings)); ?>

        <?php echo e(Form::color('pwa_background_color', trans('setting::attributes.pwa_background_color'), $errors, $settings)); ?>

        <?php echo e(Form::color('pwa_status_bar', trans('setting::attributes.pwa_status_bar'), $errors, $settings)); ?>

        <?php echo e(Form::select('pwa_display', trans('setting::attributes.pwa_display'), $errors, $displays, $settings)); ?>

        <?php echo e(Form::select('pwa_orientation', trans('setting::attributes.pwa_orientation'), $errors, $orientations, $settings)); ?>

        <?php echo e(Form::select('translatable[pwa_direction]', trans('setting::attributes.pwa_direction'), $errors, $directions, $settings)); ?>

    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Setting/Resources/views/admin/settings/tabs/pwa.blade.php ENDPATH**/ ?>