<div class="row">
    <div class="col-md-8">
        <?php echo e(Form::checkbox('iyzico_enabled', trans('setting::attributes.iyzico_enabled'), trans('setting::settings.form.enable_iyzico'), $errors, $settings)); ?>

        <?php echo e(Form::text('translatable[iyzico_label]', trans('setting::attributes.iyzico_label'), $errors, $settings, ['required' => true])); ?>

        <?php echo e(Form::textarea('translatable[iyzico_description]', trans('setting::attributes.iyzico_description'), $errors, $settings, ['rows' => 3, 'required' => true])); ?>

        <?php echo e(Form::checkbox('iyzico_test_mode', trans('setting::attributes.iyzico_test_mode'), trans('setting::settings.form.use_sandbox_for_test_payments'), $errors, $settings)); ?>


        <div class="<?php echo e(old('iyzico_enabled', array_get($settings, 'iyzico_enabled')) ? '' : 'hide'); ?>" id="iyzico-fields">
            <?php echo e(Form::select('iyzico_supported_currencies', trans('setting::attributes.supported_currencies'), $errors, $currencies, $settings, ['class' => 'selectize prevent-creation', 'required' => true, 'multiple' => true])); ?>

            <?php echo e(Form::text('iyzico_api_key', trans('setting::attributes.iyzico_api_key'), $errors, $settings, ['required' => true])); ?>

            <?php echo e(Form::password('iyzico_api_secret', trans('setting::attributes.iyzico_api_secret'), $errors, $settings, ['required' => true])); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Setting/Resources/views/admin/settings/tabs/iyzico.blade.php ENDPATH**/ ?>