<!DOCTYPE html>
<html lang="<?php echo e(locale()); ?>">
    <head>
        <base href="<?php echo e(url('/')); ?>">
        <meta charset="UTF-8">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title>
            <?php echo $__env->yieldContent('title'); ?> - <?php echo e(setting('store_name')); ?> <?php echo e(trans('admin::admin.admin')); ?>

        </title>

        <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

        <script src="<?php echo e(v(asset('build/assets/jquery.min.js'))); ?>"></script>
        <script src="<?php echo e(v(asset('build/assets/bootstrap.min.js'))); ?>"></script>
        <script src="<?php echo e(v(asset('build/assets/selectize.min.js'))); ?>"></script>

        <?php echo app('Illuminate\Foundation\Vite')([
            'modules/Admin/Resources/assets/sass/main.scss',
            'modules/Admin/Resources/assets/js/main.js',
            'modules/Admin/Resources/assets/js/app.js'
        ]); ?>

        <?php echo $__env->yieldPushContent('styles'); ?>

        <?php echo $__env->make('admin::partials.globals', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </head>

    <body class="skin-blue sidebar-mini offcanvas clearfix <?php echo e(is_rtl() ? 'rtl' : 'ltr'); ?>" dir>
        <div class="left-side"></div>

        <?php echo $__env->make('admin::partials.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="wrapper">
            <div class="content-wrapper">
                <?php echo $__env->make('admin::partials.top_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <section class="content-header clearfix">
                    <?php echo $__env->yieldContent('content_header'); ?>
                </section>

                <section class="content">
                    <?php echo $__env->make('admin::partials.notification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php echo $__env->yieldContent('content'); ?>
                </section>

                <div id="notification-toast"></div>
            </div>
        </div>

        <?php echo $__env->make('admin::partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('admin::partials.confirmation_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->yieldPushContent('scripts'); ?>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Admin/Resources/views/layout.blade.php ENDPATH**/ ?>