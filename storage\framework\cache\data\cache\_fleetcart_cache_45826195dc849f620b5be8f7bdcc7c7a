a:3:{i:0;a:55:{s:5:"redis";s:37:"Could not connect to the redis server";s:8:"accepted";s:31:"The :attribute must be accepted";s:10:"active_url";s:33:"The :attribute is not a valid URL";s:5:"after";s:41:"The :attribute must be a date after :date";s:14:"after_or_equal";s:53:"The :attribute must be a date after or equal to :date";s:5:"alpha";s:39:"The :attribute may only contain letters";s:10:"alpha_dash";s:60:"The :attribute may only contain letters, numbers, and dashes";s:9:"alpha_num";s:51:"The :attribute may only contain letters and numbers";s:5:"array";s:31:"The :attribute must be an array";s:6:"before";s:42:"The :attribute must be a date before :date";s:15:"before_or_equal";s:54:"The :attribute must be a date before or equal to :date";s:7:"between";a:4:{s:7:"numeric";s:44:"The :attribute must be between :min and :max";s:4:"file";s:54:"The :attribute must be between :min and :max kilobytes";s:6:"string";s:55:"The :attribute must be between :min and :max characters";s:5:"array";s:52:"The :attribute must have between :min and :max items";}s:7:"boolean";s:42:"The :attribute field must be true or false";s:9:"confirmed";s:42:"The :attribute confirmation does not match";s:4:"date";s:34:"The :attribute is not a valid date";s:11:"date_format";s:48:"The :attribute does not match the format :format";s:9:"different";s:43:"The :attribute and :other must be different";s:6:"digits";s:37:"The :attribute must be :digits digits";s:14:"digits_between";s:51:"The :attribute must be between :min and :max digits";s:10:"dimensions";s:43:"The :attribute has invalid image dimensions";s:8:"distinct";s:42:"The :attribute field has a duplicate value";s:5:"email";s:44:"The :attribute must be a valid email address";s:6:"exists";s:34:"The selected :attribute is invalid";s:4:"file";s:29:"The :attribute must be a file";s:6:"filled";s:38:"The :attribute field must have a value";s:5:"image";s:31:"The :attribute must be an image";s:2:"in";s:34:"The selected :attribute is invalid";s:8:"in_array";s:45:"The :attribute field does not exist in :other";s:7:"integer";s:33:"The :attribute must be an integer";s:2:"ip";s:41:"The :attribute must be a valid IP address";s:4:"ipv4";s:43:"The :attribute must be a valid IPv4 address";s:4:"ipv6";s:43:"The :attribute must be a valid IPv6 address";s:4:"json";s:42:"The :attribute must be a valid JSON string";s:3:"max";a:4:{s:7:"numeric";s:43:"The :attribute may not be greater than :max";s:4:"file";s:53:"The :attribute may not be greater than :max kilobytes";s:6:"string";s:54:"The :attribute may not be greater than :max characters";s:5:"array";s:48:"The :attribute may not have more than :max items";}s:5:"mimes";s:46:"The :attribute must be a file of type: :values";s:9:"mimetypes";s:46:"The :attribute must be a file of type: :values";s:3:"min";a:4:{s:7:"numeric";s:36:"The :attribute must be at least :min";s:4:"file";s:46:"The :attribute must be at least :min kilobytes";s:6:"string";s:47:"The :attribute must be at least :min characters";s:5:"array";s:44:"The :attribute must have at least :min items";}s:6:"not_in";s:34:"The selected :attribute is invalid";s:7:"numeric";s:31:"The :attribute must be a number";s:7:"present";s:36:"The :attribute field must be present";s:5:"regex";s:32:"The :attribute format is invalid";s:8:"required";s:32:"The :attribute field is required";s:11:"required_if";s:54:"The :attribute field is required when :other is :value";s:15:"required_unless";s:60:"The :attribute field is required unless :other is in :values";s:13:"required_with";s:56:"The :attribute field is required when :values is present";s:17:"required_with_all";s:56:"The :attribute field is required when :values is present";s:16:"required_without";s:60:"The :attribute field is required when :values is not present";s:20:"required_without_all";s:65:"The :attribute field is required when none of :values are present";s:4:"same";s:36:"The :attribute and :other must match";s:4:"size";a:4:{s:7:"numeric";s:28:"The :attribute must be :size";s:4:"file";s:38:"The :attribute must be :size kilobytes";s:6:"string";s:39:"The :attribute must be :size characters";s:5:"array";s:39:"The :attribute must contain :size items";}s:6:"string";s:31:"The :attribute must be a string";s:8:"timezone";s:35:"The :attribute must be a valid zone";s:6:"unique";s:37:"The :attribute has already been taken";s:8:"uploaded";s:31:"The :attribute failed to upload";s:3:"url";s:32:"The :attribute format is invalid";}i:1;a:1:{s:29:"_fleetcart_cache_translations";s:29:"_fleetcart_cache_translations";}i:2;i:1783449205;}