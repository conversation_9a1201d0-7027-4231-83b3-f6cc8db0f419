<div class="product-gallery position-relative align-self-start"> 
    <div class="product-gallery-preview-wrap position-relative overflow-hidden">
        <div class="product-gallery-preview swiper">
            <div class="swiper-wrapper">
                <?php if($product->media->isNotEmpty()): ?>
                    <?php $__currentLoopData = $product->media; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <div class="gallery-preview-slide">
                                <div class="gallery-preview-item" @click="triggerGalleryPreviewLightbox($event)">
                                    <img src="<?php echo e($media->path); ?>" data-zoom="<?php echo e($media->path); ?>" alt="<?php echo e($product->name); ?>">
                                </div>

                                <a href="<?php echo e($media->path); ?>" data-gallery="product-gallery-preview" class="gallery-view-icon glightbox">
                                    <i class="las la-search-plus"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="swiper-slide">
                        <div class="gallery-preview-slide">
                            <div class="gallery-preview-item" @click="triggerGalleryPreviewLightbox($event)">
                                <img src="<?php echo e(asset('build/assets/image-placeholder.png')); ?>" data-zoom="<?php echo e(asset('build/assets/image-placeholder.png')); ?>" alt="<?php echo e($product->name); ?>" class="image-placeholder">
                            </div>

                            <a href="<?php echo e(asset('build/assets/image-placeholder.png')); ?>" data-gallery="product-gallery-preview" class="gallery-view-icon glightbox">
                                <i class="las la-search-plus"></i>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </div>

    <div class="product-gallery-thumbnail swiper"> 
        <div class="swiper-wrapper">
            <?php if($product->media->isNotEmpty()): ?>
                <?php $__currentLoopData = $product->media; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="swiper-slide">
                        <div class="gallery-thumbnail-slide">
                            <div class="gallery-thumbnail-item">
                                <img src="<?php echo e($media->path); ?>" alt="<?php echo e($product->name); ?>">
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="swiper-slide">
                    <div class="gallery-thumbnail-slide">
                        <div class="gallery-thumbnail-item">
                            <img src="<?php echo e(asset('build/assets/image-placeholder.png')); ?>" alt="<?php echo e($product->name); ?>" class="image-placeholder">
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div x-cloak class="swiper-button-next"></div>
        <div x-cloak class="swiper-button-prev"></div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/gallery.blade.php ENDPATH**/ ?>