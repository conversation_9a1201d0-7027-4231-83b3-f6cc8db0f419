<div class="table-responsive">
    <table class="table table-borderless my-orders-table">
        <thead>
            <tr>
                <th><?php echo e(trans('storefront::account.orders.order_id')); ?></th> 
                <th><?php echo e(trans('storefront::account.date')); ?></th>
                <th><?php echo e(trans('storefront::account.status')); ?></th>
                <th><?php echo e(trans('storefront::account.orders.total')); ?></th>
                <th><?php echo e(trans('storefront::account.action')); ?></th>
            </tr>
        </thead>

        <tbody>
            <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>
                        <?php echo e($order->id); ?>

                    </td>
                    <td>
                        <?php echo e($order->created_at->toFormattedDateString()); ?>

                    </td>
                    <td>
                        <span class="badge <?php echo e(order_status_badge_class($order->status)); ?>">
                            <?php echo e($order->status()); ?>

                        </span>
                    </td>
                    <td>
                        <?php echo e($order->total->convert($order->currency, $order->currency_rate)->format($order->currency)); ?>

                    </td>
                    <td>
                        <a href="<?php echo e(route('account.orders.show', $order)); ?>" title="<?php echo e(trans('storefront::account.orders.view')); ?>" class="btn btn-view">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M14.3623 7.3635C14.565 7.6477 14.6663 7.78983 14.6663 8.00016C14.6663 8.2105 14.565 8.35263 14.3623 8.63683C13.4516 9.9139 11.1258 12.6668 7.99967 12.6668C4.87353 12.6668 2.54774 9.9139 1.63703 8.63683C1.43435 8.35263 1.33301 8.2105 1.33301 8.00016C1.33301 7.78983 1.43435 7.6477 1.63703 7.3635C2.54774 6.08646 4.87353 3.3335 7.99967 3.3335C11.1258 3.3335 13.4516 6.08646 14.3623 7.3635Z" stroke="white" stroke-width="1"/>
                                <path d="M10 8C10 6.8954 9.1046 6 8 6C6.8954 6 6 6.8954 6 8C6 9.1046 6.8954 10 8 10C9.1046 10 10 9.1046 10 8Z" stroke="white" stroke-width="1"/>
                            </svg>
                        </a>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/account/partials/orders_table.blade.php ENDPATH**/ ?>