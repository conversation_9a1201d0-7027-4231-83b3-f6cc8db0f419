<div class="product-details-info position-relative flex-grow-1">
    <div class="details-info-top">
        <h1 class="product-name"><?php echo e($product->name); ?></h1>

        <?php if(setting('reviews_enabled')): ?>
            <?php echo $__env->make('storefront::public.partials.product_rating', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <template x-cloak x-if="isInStock">
            <div>
                <template x-if="doesManageStock">
                    <div
                        class="availability in-stock"
                        x-text="trans('storefront::product.left_in_stock', { count: item.qty })"
                    >
                    </div>
                </template>

                <template x-if="!doesManageStock">
                    <div class="availability in-stock">
                        <?php echo e(trans('storefront::product.in_stock')); ?>

                    </div>
                </template>
            </div>
        </template>

        <template x-if="!isInStock">
            <div class="availability out-of-stock">
                <?php echo e(trans('storefront::product.out_of_stock')); ?>

            </div>
        </template>

        <div class="brief-description">
            <?php echo $product->short_description; ?>

        </div>

        <div class="details-info-top-actions">
            <button
                class="btn btn-wishlist"
                :class="{ 'added': inWishlist }"
                @click="syncWishlist"
            >
                <template x-if="inWishlist">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M16.44 3.1001C14.63 3.1001 13.01 3.9801 12 5.3301C10.99 3.9801 9.37 3.1001 7.56 3.1001C4.49 3.1001 2 5.6001 2 8.6901C2 9.8801 2.19 10.9801 2.52 12.0001C4.1 17.0001 8.97 19.9901 11.38 20.8101C11.72 20.9301 12.28 20.9301 12.62 20.8101C15.03 19.9901 19.9 17.0001 21.48 12.0001C21.81 10.9801 22 9.8801 22 8.6901C22 5.6001 19.51 3.1001 16.44 3.1001Z" fill="#292D32"/>
                    </svg>
                </template>

                <template x-if="!inWishlist">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.68998C2 5.59998 4.49 3.09998 7.56 3.09998C9.38 3.09998 10.99 3.97998 12 5.33998C13.01 3.97998 14.63 3.09998 16.44 3.09998C19.51 3.09998 22 5.59998 22 8.68998C22 15.69 15.52 19.82 12.62 20.81Z" stroke="#292D32" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </template>

                <?php echo e(trans('storefront::product.wishlist')); ?>

            </button>

            <button
                class="btn btn-compare"
                :class="{ 'added': inCompareList }"
                @click="syncCompareList"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3.58008 5.15991H17.4201C19.0801 5.15991 20.4201 6.49991 20.4201 8.15991V11.4799" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M6.74008 2L3.58008 5.15997L6.74008 8.32001" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M20.4201 18.84H6.58008C4.92008 18.84 3.58008 17.5 3.58008 15.84V12.52" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M17.26 21.9999L20.42 18.84L17.26 15.6799" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>

                <?php echo e(trans('storefront::product.compare')); ?>

            </button>
        </div>
    </div>

    <div class="details-info-middle">
        <?php if($product->variant): ?>
            <template x-if="isActiveItem">
                <div class="product-price">
                    <template x-if="hasSpecialPrice">
                        <span class="special-price" x-text="formatCurrency(specialPrice)"></span>
                    </template>

                    <span class="previous-price" x-text="formatCurrency(regularPrice)">
                        <?php echo $item->is_active ? $item->hasSpecialPrice() ? $item->special_price->format() : $item->price->format() : ''; ?>

                    </span>
                </div>
            </template>
        <?php else: ?>
            <div class="product-price">
                <template x-if="hasSpecialPrice">
                    <span class="special-price" x-text="formatCurrency(specialPrice)"></span>
                </template>

                <span class="previous-price" x-text="formatCurrency(regularPrice)">
                    <?php echo e($item->hasSpecialPrice() ? $item->special_price->format() : $item->price->format()); ?>

                </span>
            </div>
        <?php endif; ?>

        <form
            @input="errors.clear($event.target.name)"
            @submit.prevent="addToCart"
        >
            <?php if($product->variant): ?>
                <div class="product-variants">
                    <?php echo $__env->make('storefront::public.products.show.variations', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            <?php endif; ?>

            <?php if($product->options->isNotEmpty()): ?>
                <div class="product-variants">
                    <?php $__currentLoopData = $product->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if ($__env->exists("storefront::public.products.show.custom_options.{$option->type}")) echo $__env->make("storefront::public.products.show.custom_options.{$option->type}", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>

            <div class="details-info-middle-actions">
                <div class="number-picker-lg">
                    <label for="qty"><?php echo e(trans('storefront::product.quantity')); ?></label>

                    <div class="input-group-quantity">
                        <input
                            x-ref="inputQuantity"
                            type="text"
                            :value="cartItemForm.qty"
                            min="1"
                            :max="maxQuantity"
                            id="qty"
                            class="form-control input-number input-quantity"
                            :disabled="isAddToCartDisabled"
                            @focus="$event.target.select()"
                            @input="updateQuantity(Number($event.target.value))"
                            @keydown.up="updateQuantity(cartItemForm.qty + 1)"
                            @keydown.down="updateQuantity(cartItemForm.qty - 1)"
                        >

                        <span class="btn-wrapper">
                            <button
                                type="button"
                                aria-label="quantity"
                                class="btn btn-number btn-plus"
                                :disabled="isQtyIncreaseDisabled"
                                @click="updateQuantity(cartItemForm.qty + 1)"
                            >
                                +
                            </button>

                            <button
                                type="button"
                                aria-label="quantity"
                                class="btn btn-number btn-minus"
                                :disabled="isQtyDecreaseDisabled"
                                @click="updateQuantity(cartItemForm.qty - 1)"
                            >
                                -
                            </button>
                        </span>
                    </div>
                </div>

                <button
                    type="submit"
                    class="btn btn-primary btn-add-to-cart"
                    :class="{'btn-loading': addingToCart }"
                    :disabled="isAddToCartDisabled"
                    x-text="isActiveItem ? '<?php echo e(trans('storefront::product.add_to_cart')); ?>' : '<?php echo e(trans('storefront::product.unavailable')); ?>'"
                >
                    <?php echo e(trans($item->is_active ? 'storefront::product.add_to_cart' : 'storefront::product.unavailable')); ?>

                </button>

                <!-- Buy Now Button -->
                <button
                    type="button"
                    class="btn btn-primary btn-buy-now"
                    :disabled="isAddToCartDisabled"
                    @click="buyNow"
                    style="margin-left:5px;margin-right:5px;"
                >
                    <?php echo e(trans('storefront::product.buy_now')); ?>

                </button>
            </div>
        </form>
    </div>

    <div class="details-info-bottom">
        <ul class="list-inline additional-info">
            <template x-cloak x-if="item.sku">
                <li class="sku">
                    <label><?php echo e(trans('storefront::product.sku')); ?></label>

                    <span x-text="item.sku"><?php echo e($item->sku); ?></span>
                </li>
            </template>

            <?php if($product->categories->isNotEmpty()): ?>
                <li>
                    <label><?php echo e(trans('storefront::product.categories')); ?></label>

                    <?php $__currentLoopData = $product->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($category->url()); ?>"><?php echo e($category->name); ?></a><?php echo e($loop->last ? '' : ','); ?>

                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </li>
            <?php endif; ?>

            <?php if($product->tags->isNotEmpty()): ?>
                <li>
                    <label><?php echo e(trans('storefront::product.tags')); ?></label>

                    <?php $__currentLoopData = $product->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($tag->url()); ?>"><?php echo e($tag->name); ?></a><?php echo e($loop->last ? '' : ','); ?>

                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </li>
            <?php endif; ?>
        </ul>

        <?php echo $__env->make('storefront::public.products.show.social_share', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/details.blade.php ENDPATH**/ ?>