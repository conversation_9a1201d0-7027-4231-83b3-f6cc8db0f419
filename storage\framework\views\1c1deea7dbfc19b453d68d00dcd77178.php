<div id="description" class="tab-pane description custom-page-content active">
    <div
        x-ref="descriptionContent" 
        class="content"
        :class="{ 
            active: showDescriptionContent,
            'less-content': !showMore }
        "
    >
        <?php echo $product->description; ?>

    </div>

    <button
        x-cloak
        type="button"
        class="btn btn-default btn-show-more"
        :class="{ 'show': showMore }"
        @click="toggleDescriptionContent"
        x-text="
            showDescriptionContent ?
            '<?php echo e(trans('storefront::product.show_less')); ?>' :
            '<?php echo e(trans('storefront::product.show_more')); ?>'
        "
    >
    </button>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/tab_description.blade.php ENDPATH**/ ?>