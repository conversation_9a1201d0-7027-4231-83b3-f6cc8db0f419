a:3:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:1;s:7:"user_id";i:1;s:8:"filename";s:28:"jacar_Nero AI_Photo_Face.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/VokNCi9vu55NUI6rjsNtJqKR8LDRlzEPz5D1imMp.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"825060";s:10:"created_at";s:19:"2025-02-28 20:44:02";s:10:"updated_at";s:19:"2025-02-28 20:44:02";}s:11:" * original";a:10:{s:2:"id";i:1;s:7:"user_id";i:1;s:8:"filename";s:28:"jacar_Nero AI_Photo_Face.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/VokNCi9vu55NUI6rjsNtJqKR8LDRlzEPz5D1imMp.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:6:"825060";s:10:"created_at";s:19:"2025-02-28 20:44:02";s:10:"updated_at";s:19:"2025-02-28 20:44:02";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;a:0:{}i:2;i:1783448003;}