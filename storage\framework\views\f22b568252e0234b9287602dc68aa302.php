<?php $__env->startSection('title', $product->name); ?>

<?php $__env->startPush('meta'); ?>
    <meta name="title" content="<?php echo e($product->meta->meta_title ?: $product->name); ?>">
    <meta name="description" content="<?php echo e($product->meta->meta_description ?: $product->short_description); ?>">
    <meta name="twitter:card" content="summary">
    <meta property="og:type" content="product">
    <meta property="og:url" content="<?php echo e($product->variant?->url() ?? $product->url()); ?>">
    <meta property="og:title" content="<?php echo e($product->meta->meta_title ?: $product->name); ?>">
    <meta property="og:description" content="<?php echo e($product->meta->meta_description ?: $product->short_description); ?>">
    <meta property="og:image" content="<?php echo e(($product->variant && $product->variant->base_image->id) ? $product->variant->base_image?->path : $product->base_image?->path ?? asset('build/assets/image-placeholder.png')); ?>">
    <meta property="og:locale" content="<?php echo e(locale()); ?>">

    <?php $__currentLoopData = supported_locale_keys(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <meta property="og:locale:alternate" content="<?php echo e($code); ?>">
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <meta property="product:price:amount" content="<?php echo e($product->variant?->selling_price->convertToCurrentCurrency()->amount() ?? $product->selling_price->convertToCurrentCurrency()->amount()); ?>">
    <meta property="product:price:currency" content="<?php echo e(currency()); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <?php if(!$categoryBreadcrumb): ?>
        <li><a href="<?php echo e(route('products.index')); ?>"><?php echo e(trans('storefront::products.shop')); ?></a></li>
    <?php endif; ?>

    <?php echo $categoryBreadcrumb; ?>


    <li class="active"><?php echo e($product->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section
        x-data="ProductShow({
            product: <?php echo e($product); ?>,
            
            <?php if($product->variant): ?>
                variant: <?php echo e($product->variant); ?>,
            <?php endif; ?>

            reviewCount: <?php echo e($review->count ?? 0); ?>,
            avgRating: <?php echo e($review->avg_rating ?? 0); ?>,
        })"
        class="product-details-wrap"
    >
        <div class="container">
            <div class="product-details-top">
                <div class="d-flex flex-column flex-lg-row flex-lg-nowrap ">
                    <?php if($product->variant): ?>
                        <?php echo $__env->make('storefront::public.products.show.variant_gallery', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php else: ?>
                        <?php echo $__env->make('storefront::public.products.show.gallery', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endif; ?> 
                    
                    <?php echo $__env->make('storefront::public.products.show.details', ['item' => $product->variant ?? $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php if(setting('storefront_features_section_enabled')): ?>
                        <?php echo $__env->make('storefront::public.products.show.right_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="product-details-bottom flex-column-reverse flex-lg-row">
                <?php echo $__env->make('storefront::public.products.show.left_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div class="product-details-bottom-inner">
                    <div class="product-details-tab clearfix">
                        <div class="product-details-tab-overflow">
                            <ul class="nav nav-tabs tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a href="#description" data-bs-toggle="tab" class="nav-link active">
                                        <?php echo e(trans('storefront::product.description')); ?>

                                    </a>
                                </li>

                                <?php if($product->hasAnyAttribute()): ?>
                                    <li class="nav-item" role="presentation">
                                        <a href="#specification" data-bs-toggle="tab" class="nav-link">
                                            <?php echo e(trans('storefront::product.specification')); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if(setting('reviews_enabled')): ?>
                                    <li class="nav-item" role="presentation">
                                        <a
                                            href="#reviews"
                                            data-bs-toggle="tab"
                                            class="nav-link"
                                            x-text="trans('storefront::product.reviews', { count: totalReviews })"
                                        >
                                            <?php echo e(trans('storefront::product.reviews', ['count' => $product->reviews->count() ])); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>

                            <hr>
                        </div>

                        <div class="tab-content">
                            <?php echo $__env->make('storefront::public.products.show.tab_description', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php echo $__env->make('storefront::public.products.show.tab_specification', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php echo $__env->make('storefront::public.products.show.tab_reviews', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    </div>

                    <?php if($relatedProducts->isNotEmpty()): ?>
                        <?php echo $__env->make('storefront::public.products.show.related_products', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('globals'); ?>
    <?php echo $productSchemaMarkup->toScript(); ?>

    
    <script>
        FleetCart.langs['storefront::product.left_in_stock'] = '<?php echo e(trans('storefront::product.left_in_stock')); ?>';
        FleetCart.langs['storefront::product.reviews'] = '<?php echo e(trans("storefront::product.reviews")); ?>';
        FleetCart.langs['storefront::product.review_submitted'] = '<?php echo e(trans("storefront::product.review_submitted")); ?>';
    </script>

    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Storefront/Resources/assets/public/sass/pages/products/show/main.scss', 
        'modules/Storefront/Resources/assets/public/js/pages/products/show/main.js',
        'modules/Storefront/Resources/assets/public/js/vendors/flatpickr.js'
    ]); ?>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('storefront::public.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show.blade.php ENDPATH**/ ?>