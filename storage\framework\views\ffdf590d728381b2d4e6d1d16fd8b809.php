<div class="row">
    <div class="col-md-8">
        <?php echo e(Form::checkbox('authorizenet_enabled', trans('setting::attributes.authorizenet_enabled'), trans('setting::settings.form.enable_authorizenet'), $errors, $settings)); ?>

        <?php echo e(Form::text('translatable[authorizenet_label]', trans('setting::attributes.translatable.authorizenet_label'), $errors, $settings, ['required' => true])); ?>

        <?php echo e(Form::textarea('translatable[authorizenet_description]', trans('setting::attributes.translatable.authorizenet_description'), $errors, $settings, ['rows' => 3, 'required' => true])); ?>

        <?php echo e(Form::checkbox('authorizenet_test_mode', trans('setting::attributes.authorizenet_test_mode'), trans('setting::settings.form.use_sandbox_for_test_payments'), $errors, $settings)); ?>


        <div class="<?php echo e(old('authorizenet_enabled', array_get($settings, 'authorizenet_enabled')) ? '' : 'hide'); ?>"
             id="authorizenet-fields">
            <?php echo e(Form::text('authorizenet_merchant_login_id', trans('setting::attributes.authorizenet_merchant_login_id'), $errors, $settings, ['required' => true])); ?>

            <?php echo e(Form::text('authorizenet_merchant_transaction_key', trans('setting::attributes.authorizenet_merchant_transaction_key'), $errors, $settings, ['required' => true])); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Setting/Resources/views/admin/settings/tabs/authorizenet.blade.php ENDPATH**/ ?>