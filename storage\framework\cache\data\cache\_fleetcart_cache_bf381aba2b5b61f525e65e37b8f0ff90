a:3:{i:0;a:165:{s:19:"supported_countries";s:19:"Supported Countries";s:15:"default_country";s:15:"Default Country";s:17:"supported_locales";s:17:"Supported Locales";s:14:"default_locale";s:14:"Default Locale";s:16:"default_timezone";s:16:"Default Timezone";s:13:"customer_role";s:13:"Customer Role";s:15:"reviews_enabled";s:17:"Reviews & Ratings";s:20:"auto_approve_reviews";s:20:"Auto Approve Reviews";s:18:"cookie_bar_enabled";s:10:"Cookie Bar";s:16:"maintenance_mode";s:16:"Maintenance Mode";s:26:"translatable.store_tagline";s:13:"Store Tagline";s:23:"translatable.store_name";s:10:"Store Name";s:11:"store_phone";s:11:"Store Phone";s:11:"store_email";s:11:"Store Email";s:15:"store_address_1";s:15:"Store Address 1";s:15:"store_address_2";s:15:"Store Address 2";s:10:"store_city";s:10:"Store City";s:13:"store_country";s:13:"Store Country";s:11:"store_state";s:11:"Store State";s:9:"store_zip";s:9:"Store Zip";s:16:"store_phone_hide";s:16:"Hide Store Phone";s:16:"store_email_hide";s:16:"Hide Store Email";s:11:"pwa_enabled";s:6:"Status";s:8:"pwa_icon";s:4:"Icon";s:15:"pwa_theme_color";s:11:"Theme Color";s:20:"pwa_background_color";s:16:"Background Color";s:14:"pwa_status_bar";s:10:"Status Bar";s:11:"pwa_display";s:7:"Display";s:15:"pwa_orientation";s:11:"Orientation";s:13:"pwa_direction";s:9:"Direction";s:18:"supported_currency";s:18:"Supported Currency";s:20:"supported_currencies";s:20:"Supported Currencies";s:16:"default_currency";s:16:"Default Currency";s:30:"currency_rate_exchange_service";s:21:"Exchange Rate Service";s:16:"fixer_access_key";s:16:"Fixer Access key";s:13:"forge_api_key";s:13:"Forge API key";s:26:"currency_data_feed_api_key";s:26:"Currency Data Feed API Key";s:27:"auto_refresh_currency_rates";s:12:"Auto Refresh";s:36:"auto_refresh_currency_rate_frequency";s:9:"Frequency";s:8:"sms_from";s:8:"SMS From";s:11:"sms_service";s:11:"SMS Service";s:10:"vonage_key";s:7:"API Key";s:13:"vonage_secret";s:10:"API Secret";s:10:"twilio_sid";s:11:"Account SID";s:12:"twilio_token";s:10:"Auth Token";s:10:"htd_api_id";s:10:"HTD API ID";s:11:"welcome_sms";s:11:"Welcome SMS";s:19:"new_order_admin_sms";s:19:"New Order Admin SMS";s:13:"new_order_sms";s:13:"New Order SMS";s:18:"sms_order_statuses";s:18:"SMS Order Statuses";s:17:"mail_from_address";s:17:"Mail From Address";s:14:"mail_from_name";s:14:"Mail From Name";s:9:"mail_host";s:9:"Mail Host";s:9:"mail_port";s:9:"Mail Port";s:13:"mail_username";s:13:"Mail Username";s:13:"mail_password";s:13:"Mail Password";s:15:"mail_encryption";s:15:"Mail Encryption";s:13:"welcome_email";s:13:"Welcome Email";s:17:"admin_order_email";s:21:"New Order Admin Email";s:13:"invoice_email";s:13:"Invoice Email";s:20:"email_order_statuses";s:20:"Email Order Statuses";s:18:"newsletter_enabled";s:10:"Newsletter";s:17:"mailchimp_api_key";s:17:"Mailchimp API Key";s:17:"mailchimp_list_id";s:17:"Mailchimp List ID";s:24:"google_recaptcha_enabled";s:6:"Status";s:25:"google_recaptcha_site_key";s:8:"Site Key";s:27:"google_recaptcha_secret_key";s:10:"Secret Key";s:20:"custom_header_assets";s:6:"Header";s:20:"custom_footer_assets";s:6:"Footer";s:22:"facebook_login_enabled";s:6:"Status";s:21:"facebook_login_app_id";s:6:"App ID";s:25:"facebook_login_app_secret";s:10:"App Secret";s:20:"google_login_enabled";s:6:"Status";s:22:"google_login_client_id";s:9:"Client ID";s:26:"google_login_client_secret";s:13:"Client Secret";s:21:"free_shipping_enabled";s:6:"Status";s:32:"translatable.free_shipping_label";s:5:"Label";s:24:"free_shipping_min_amount";s:14:"Minimum Amount";s:20:"local_pickup_enabled";s:6:"Status";s:31:"translatable.local_pickup_label";s:5:"Label";s:17:"local_pickup_cost";s:4:"Cost";s:17:"flat_rate_enabled";s:6:"Status";s:28:"translatable.flat_rate_label";s:5:"Label";s:14:"flat_rate_cost";s:4:"Cost";s:14:"paypal_enabled";s:6:"Status";s:25:"translatable.paypal_label";s:5:"Label";s:31:"translatable.paypal_description";s:11:"Description";s:16:"paypal_test_mode";s:7:"Sandbox";s:16:"paypal_client_id";s:9:"Client ID";s:13:"paypal_secret";s:6:"Secret";s:14:"stripe_enabled";s:6:"Status";s:25:"translatable.stripe_label";s:5:"Label";s:31:"translatable.stripe_description";s:11:"Description";s:22:"stripe_publishable_key";s:15:"Publishable Key";s:17:"stripe_secret_key";s:10:"Secret Key";s:23:"stripe_integration_type";s:16:"Integration Type";s:13:"paytm_enabled";s:6:"Status";s:24:"translatable.paytm_label";s:5:"Label";s:30:"translatable.paytm_description";s:11:"Description";s:15:"paytm_test_mode";s:7:"Sandbox";s:17:"paytm_merchant_id";s:11:"Merchant ID";s:18:"paytm_merchant_key";s:12:"Merchant Key";s:16:"razorpay_enabled";s:6:"Status";s:27:"translatable.razorpay_label";s:5:"Label";s:33:"translatable.razorpay_description";s:11:"Description";s:15:"razorpay_key_id";s:6:"Key Id";s:19:"razorpay_key_secret";s:10:"Key Secret";s:17:"instamojo_enabled";s:6:"Status";s:28:"translatable.instamojo_label";s:5:"Label";s:34:"translatable.instamojo_description";s:11:"Description";s:19:"instamojo_test_mode";s:7:"Sandbox";s:17:"instamojo_api_key";s:7:"API Key";s:20:"instamojo_auth_token";s:10:"Auth Token";s:16:"paystack_enabled";s:6:"Status";s:27:"translatable.paystack_label";s:5:"Label";s:33:"translatable.paystack_description";s:11:"Description";s:18:"paystack_test_mode";s:7:"Sandbox";s:19:"paystack_public_key";s:10:"Public Key";s:19:"paystack_secret_key";s:10:"Secret Key";s:20:"authorizenet_enabled";s:6:"Status";s:31:"translatable.authorizenet_label";s:5:"Label";s:37:"translatable.authorizenet_description";s:11:"Description";s:22:"authorizenet_test_mode";s:7:"Sandbox";s:30:"authorizenet_merchant_login_id";s:17:"Merchant Login ID";s:37:"authorizenet_merchant_transaction_key";s:24:"Merchant Transaction Key";s:19:"mercadopago_enabled";s:6:"Status";s:30:"translatable.mercadopago_label";s:5:"Label";s:36:"translatable.mercadopago_description";s:11:"Description";s:21:"mercadopago_test_mode";s:7:"Sandbox";s:22:"mercadopago_public_key";s:10:"Public Key";s:24:"mercadopago_access_token";s:12:"Access Token";s:19:"flutterwave_enabled";s:6:"Status";s:30:"translatable.flutterwave_label";s:5:"Label";s:36:"translatable.flutterwave_description";s:11:"Description";s:21:"flutterwave_test_mode";s:7:"Sandbox";s:22:"flutterwave_public_key";s:10:"Public Key";s:22:"flutterwave_secret_key";s:10:"Secret Key";s:26:"flutterwave_encryption_key";s:14:"Encryption Key";s:14:"iyzico_enabled";s:6:"Status";s:12:"iyzico_label";s:5:"Label";s:18:"iyzico_description";s:11:"Description";s:16:"iyzico_test_mode";s:7:"Sandbox";s:14:"iyzico_api_key";s:7:"API Key";s:17:"iyzico_api_secret";s:10:"API Secret";s:15:"payfast_enabled";s:6:"Status";s:26:"translatable.payfast_label";s:5:"Label";s:32:"translatable.payfast_description";s:11:"Description";s:17:"payfast_test_mode";s:7:"Sandbox";s:19:"payfast_merchant_id";s:11:"Merchant ID";s:20:"payfast_merchant_key";s:12:"Merchant Key";s:18:"payfast_passphrase";s:10:"Passphrase";s:11:"cod_enabled";s:6:"Status";s:22:"translatable.cod_label";s:5:"Label";s:28:"translatable.cod_description";s:11:"Description";s:21:"bank_transfer_enabled";s:6:"Status";s:32:"translatable.bank_transfer_label";s:5:"Label";s:38:"translatable.bank_transfer_description";s:11:"Description";s:39:"translatable.bank_transfer_instructions";s:12:"Instructions";s:21:"check_payment_enabled";s:6:"Status";s:32:"translatable.check_payment_label";s:5:"Label";s:38:"translatable.check_payment_description";s:11:"Description";s:39:"translatable.check_payment_instructions";s:12:"Instructions";s:21:"supported_countries.*";s:19:"Supported Countries";s:19:"supported_locales.*";s:17:"Supported Locales";s:22:"supported_currencies.*";s:20:"Supported Currencies";}i:1;a:1:{s:29:"_fleetcart_cache_translations";s:29:"_fleetcart_cache_translations";}i:2;i:**********;}