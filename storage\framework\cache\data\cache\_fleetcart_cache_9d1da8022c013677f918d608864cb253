a:3:{i:0;O:26:"TypiCMS\NestableCollection":8:{s:8:" * items";a:9:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:20;s:9:"parent_id";i:19;s:4:"slug";s:9:"ardyat-9d";s:8:"position";i:1;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:30:25";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:20;s:9:"parent_id";i:19;s:4:"slug";s:9:"ardyat-9d";s:8:"position";i:1;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:30:25";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:20;s:11:"category_id";i:20;s:6:"locale";s:5:"ar_PS";s:4:"name";s:23:"ارضيات سيارة";}s:11:" * original";a:4:{s:2:"id";i:20;s:11:"category_id";i:20;s:6:"locale";s:5:"ar_PS";s:4:"name";s:23:"ارضيات سيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:23;s:9:"parent_id";i:19;s:4:"slug";s:23:"ardy-sndok-khlfy-standr";s:8:"position";i:2;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:32:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:23;s:9:"parent_id";i:19;s:4:"slug";s:23:"ardy-sndok-khlfy-standr";s:8:"position";i:2;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:32:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:23;s:11:"category_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:30:"ارضية صندوق خلفي";}s:11:" * original";a:4:{s:2:"id";i:23;s:11:"category_id";i:23;s:6:"locale";s:5:"ar_PS";s:4:"name";s:30:"ارضية صندوق خلفي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:11:" * original";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:938;s:7:"user_id";i:1;s:8:"filename";s:11:"mat (1).png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/dLjF6PsbRwKyUhjrFxMaSNGJIsXyywTiX1olB3VG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"14006";s:10:"created_at";s:19:"2025-07-02 02:08:32";s:10:"updated_at";s:19:"2025-07-02 02:08:32";}s:11:" * original";a:17:{s:2:"id";i:938;s:7:"user_id";i:1;s:8:"filename";s:11:"mat (1).png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/dLjF6PsbRwKyUhjrFxMaSNGJIsXyywTiX1olB3VG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"14006";s:10:"created_at";s:19:"2025-07-02 02:08:32";s:10:"updated_at";s:19:"2025-07-02 02:08:32";s:15:"pivot_entity_id";i:19;s:13:"pivot_file_id";i:938;s:17:"pivot_entity_type";s:34:"Modules\Category\Entities\Category";s:8:"pivot_id";i:1764;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-07-02 02:08:41";s:16:"pivot_updated_at";s:19:"2025-07-02 02:08:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:19;s:7:"file_id";i:938;s:2:"id";i:1764;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:08:41";s:10:"updated_at";s:19:"2025-07-02 02:08:41";}s:11:" * original";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:19;s:7:"file_id";i:938;s:2:"id";i:1764;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:08:41";s:10:"updated_at";s:19:"2025-07-02 02:08:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";N;s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:34:"Modules\Category\Entities\Category";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:15;s:9:"parent_id";N;s:4:"slug";s:22:"frsh-almkaaad-omlhkath";s:8:"position";i:3;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:38:40";s:10:"updated_at";s:19:"2025-07-02 02:12:25";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:16;s:9:"parent_id";i:15;s:4:"slug";s:11:"frsh-mkaaad";s:8:"position";i:4;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:39:21";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:16;s:9:"parent_id";i:15;s:4:"slug";s:11:"frsh-mkaaad";s:8:"position";i:4;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:39:21";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:16;s:11:"category_id";i:16;s:6:"locale";s:5:"ar_PS";s:4:"name";s:17:"فرش مقاعد";}s:11:" * original";a:4:{s:2:"id";i:16;s:11:"category_id";i:16;s:6:"locale";s:5:"ar_PS";s:4:"name";s:17:"فرش مقاعد";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:17;s:9:"parent_id";i:15;s:4:"slug";s:13:"ghtaaa-styrnk";s:8:"position";i:5;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:40:20";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:17;s:9:"parent_id";i:15;s:4:"slug";s:13:"ghtaaa-styrnk";s:8:"position";i:5;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:40:20";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:17;s:11:"category_id";i:17;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"غطاء ستيرنك";}s:11:" * original";a:4:{s:2:"id";i:17;s:11:"category_id";i:17;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"غطاء ستيرنك";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:18;s:9:"parent_id";i:15;s:4:"slug";s:9:"msand-tby";s:8:"position";i:6;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:40:42";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:18;s:9:"parent_id";i:15;s:4:"slug";s:9:"msand-tby";s:8:"position";i:6;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:40:42";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:18;s:11:"category_id";i:18;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"مساند طبية";}s:11:" * original";a:4:{s:2:"id";i:18;s:11:"category_id";i:18;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"مساند طبية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:15;s:9:"parent_id";N;s:4:"slug";s:22:"frsh-almkaaad-omlhkath";s:8:"position";i:3;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:38:40";s:10:"updated_at";s:19:"2025-07-02 02:12:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:15;s:11:"category_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:11:" * original";a:4:{s:2:"id";i:15;s:11:"category_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:940;s:7:"user_id";i:1;s:8:"filename";s:12:"car-seat.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/9KzPH7luIs5jmnLFX26BchL5MhgpmQQyNygys2Hb.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"14881";s:10:"created_at";s:19:"2025-07-02 02:12:20";s:10:"updated_at";s:19:"2025-07-02 02:12:20";}s:11:" * original";a:17:{s:2:"id";i:940;s:7:"user_id";i:1;s:8:"filename";s:12:"car-seat.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/9KzPH7luIs5jmnLFX26BchL5MhgpmQQyNygys2Hb.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"14881";s:10:"created_at";s:19:"2025-07-02 02:12:20";s:10:"updated_at";s:19:"2025-07-02 02:12:20";s:15:"pivot_entity_id";i:15;s:13:"pivot_file_id";i:940;s:17:"pivot_entity_type";s:34:"Modules\Category\Entities\Category";s:8:"pivot_id";i:1766;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-07-02 02:12:25";s:16:"pivot_updated_at";s:19:"2025-07-02 02:12:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:15;s:7:"file_id";i:940;s:2:"id";i:1766;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:12:25";s:10:"updated_at";s:19:"2025-07-02 02:12:25";}s:11:" * original";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:15;s:7:"file_id";i:940;s:2:"id";i:1766;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:12:25";s:10:"updated_at";s:19:"2025-07-02 02:12:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:414;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:34:"Modules\Category\Entities\Category";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:49;s:9:"parent_id";i:25;s:4:"slug";s:16:"akssoarat-mtnoaa";s:8:"position";i:8;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-16 19:04:08";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:49;s:9:"parent_id";i:25;s:4:"slug";s:16:"akssoarat-mtnoaa";s:8:"position";i:8;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-16 19:04:08";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:49;s:11:"category_id";i:49;s:6:"locale";s:5:"ar_PS";s:4:"name";s:29:"اكسسوارات ديكور";}s:11:" * original";a:4:{s:2:"id";i:49;s:11:"category_id";i:49;s:6:"locale";s:5:"ar_PS";s:4:"name";s:29:"اكسسوارات ديكور";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:26;s:9:"parent_id";i:25;s:4:"slug";s:9:"rkay-osty";s:8:"position";i:9;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:43:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:26;s:9:"parent_id";i:25;s:4:"slug";s:9:"rkay-osty";s:8:"position";i:9;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:43:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:26;s:11:"category_id";i:26;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"ركاية وسطية";}s:11:" * original";a:4:{s:2:"id";i:26;s:11:"category_id";i:26;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"ركاية وسطية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:48;s:9:"parent_id";i:25;s:4:"slug";s:27:"mlhkat-alhtaf-almhmol-lsyar";s:8:"position";i:10;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-16 18:26:32";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:48;s:9:"parent_id";i:25;s:4:"slug";s:27:"mlhkat-alhtaf-almhmol-lsyar";s:8:"position";i:10;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-16 18:26:32";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:48;s:11:"category_id";i:48;s:6:"locale";s:5:"ar_PS";s:4:"name";s:40:"ملحقات الهاتف المحمول";}s:11:" * original";a:4:{s:2:"id";i:48;s:11:"category_id";i:48;s:6:"locale";s:5:"ar_PS";s:4:"name";s:40:"ملحقات الهاتف المحمول";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:11:" * original";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:941;s:7:"user_id";i:1;s:8:"filename";s:12:"computer.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/hxUoAfAR7YregQvEQ359QD8c1MzP3pKDbflc63cG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"34850";s:10:"created_at";s:19:"2025-07-02 02:14:17";s:10:"updated_at";s:19:"2025-07-02 02:14:17";}s:11:" * original";a:17:{s:2:"id";i:941;s:7:"user_id";i:1;s:8:"filename";s:12:"computer.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/hxUoAfAR7YregQvEQ359QD8c1MzP3pKDbflc63cG.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:5:"34850";s:10:"created_at";s:19:"2025-07-02 02:14:17";s:10:"updated_at";s:19:"2025-07-02 02:14:17";s:15:"pivot_entity_id";i:25;s:13:"pivot_file_id";i:941;s:17:"pivot_entity_type";s:34:"Modules\Category\Entities\Category";s:8:"pivot_id";i:1767;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-07-02 02:14:23";s:16:"pivot_updated_at";s:19:"2025-07-02 02:14:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:25;s:7:"file_id";i:941;s:2:"id";i:1767;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:14:23";s:10:"updated_at";s:19:"2025-07-02 02:14:23";}s:11:" * original";a:7:{s:11:"entity_type";s:34:"Modules\Category\Entities\Category";s:9:"entity_id";i:25;s:7:"file_id";i:941;s:2:"id";i:1767;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-07-02 02:14:23";s:10:"updated_at";s:19:"2025-07-02 02:14:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:414;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:34:"Modules\Category\Entities\Category";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:3;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:36;s:9:"parent_id";N;s:4:"slug";s:19:"maadat-oadoat-lsyar";s:8:"position";i:11;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:38:51";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:36;s:9:"parent_id";N;s:4:"slug";s:19:"maadat-oadoat-lsyar";s:8:"position";i:11;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:38:51";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:36;s:11:"category_id";i:36;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:11:" * original";a:4:{s:2:"id";i:36;s:11:"category_id";i:36;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:4;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:35;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-khargy-mtnoaa";s:8:"position";i:12;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 03:14:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:35;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-khargy-mtnoaa";s:8:"position";i:12;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 03:14:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:35;s:11:"category_id";i:35;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات خارجية متنوعة";}s:11:" * original";a:4:{s:2:"id";i:35;s:11:"category_id";i:35;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات خارجية متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:5;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:32;s:9:"parent_id";N;s:4:"slug";s:15:"alaanay-balmrkb";s:8:"position";i:13;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:29:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:33;s:9:"parent_id";i:32;s:4:"slug";s:19:"mnthfat-omoad-aanay";s:8:"position";i:14;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:30:42";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:33;s:9:"parent_id";i:32;s:4:"slug";s:19:"mnthfat-omoad-aanay";s:8:"position";i:14;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:30:42";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:33;s:11:"category_id";i:33;s:6:"locale";s:5:"ar_PS";s:4:"name";s:34:"منظفات ومواد عناية";}s:11:" * original";a:4:{s:2:"id";i:33;s:11:"category_id";i:33;s:6:"locale";s:5:"ar_PS";s:4:"name";s:34:"منظفات ومواد عناية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:34;s:9:"parent_id";i:32;s:4:"slug";s:12:"aator-mtnoaa";s:8:"position";i:15;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:31:12";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:34;s:9:"parent_id";i:32;s:4:"slug";s:12:"aator-mtnoaa";s:8:"position";i:15;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:31:12";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:34;s:11:"category_id";i:34;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"عطور متنوعة";}s:11:" * original";a:4:{s:2:"id";i:34;s:11:"category_id";i:34;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"عطور متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:32;s:9:"parent_id";N;s:4:"slug";s:15:"alaanay-balmrkb";s:8:"position";i:13;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:29:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:32;s:11:"category_id";i:32;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:11:" * original";a:4:{s:2:"id";i:32;s:11:"category_id";i:32;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:6;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:29;s:9:"parent_id";N;s:4:"slug";s:24:"alanar-oalaghz-alkhrbayy";s:8:"position";i:16;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:14";s:10:"updated_at";s:19:"2025-06-30 19:01:18";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:31;s:9:"parent_id";i:29;s:4:"slug";s:11:"adaaaat-led";s:8:"position";i:17;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:04:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:31;s:9:"parent_id";i:29;s:4:"slug";s:11:"adaaaat-led";s:8:"position";i:17;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:04:49";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:31;s:11:"category_id";i:31;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"اضاءات LED";}s:11:" * original";a:4:{s:2:"id";i:31;s:11:"category_id";i:31;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"اضاءات LED";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:30;s:9:"parent_id";i:29;s:4:"slug";s:12:"aghz-khrbayy";s:8:"position";i:18;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:43";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:30;s:9:"parent_id";i:29;s:4:"slug";s:12:"aghz-khrbayy";s:8:"position";i:18;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:43";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:30;s:11:"category_id";i:30;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"اجهزة كهربائية";}s:11:" * original";a:4:{s:2:"id";i:30;s:11:"category_id";i:30;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"اجهزة كهربائية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:50;s:9:"parent_id";i:29;s:4:"slug";s:18:"alsotyat-oalmsglat";s:8:"position";i:19;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-19 20:48:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:50;s:9:"parent_id";i:29;s:4:"slug";s:18:"alsotyat-oalmsglat";s:8:"position";i:19;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-19 20:48:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:50;s:11:"category_id";i:50;s:6:"locale";s:5:"ar_PS";s:4:"name";s:35:"الصوتيات والمسجلات";}s:11:" * original";a:4:{s:2:"id";i:50;s:11:"category_id";i:50;s:6:"locale";s:5:"ar_PS";s:4:"name";s:35:"الصوتيات والمسجلات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:29;s:9:"parent_id";N;s:4:"slug";s:24:"alanar-oalaghz-alkhrbayy";s:8:"position";i:16;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:14";s:10:"updated_at";s:19:"2025-06-30 19:01:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:29;s:11:"category_id";i:29;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:11:" * original";a:4:{s:2:"id";i:29;s:11:"category_id";i:29;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:7;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:37;s:9:"parent_id";N;s:4:"slug";s:33:"taadylat-oadafat-llgybat-oaltnadr";s:8:"position";i:20;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:45:41";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:7:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:39;s:9:"parent_id";i:37;s:4:"slug";s:6:"honday";s:8:"position";i:21;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:47:15";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:4:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:42;s:9:"parent_id";i:39;s:4:"slug";s:5:"tosan";s:8:"position";i:22;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:42;s:9:"parent_id";i:39;s:4:"slug";s:5:"tosan";s:8:"position";i:22;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:42;s:11:"category_id";i:42;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"توسان";}s:11:" * original";a:4:{s:2:"id";i:42;s:11:"category_id";i:42;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"توسان";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:43;s:9:"parent_id";i:39;s:4:"slug";s:6:"sntafy";s:8:"position";i:23;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:49:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:43;s:9:"parent_id";i:39;s:4:"slug";s:6:"sntafy";s:8:"position";i:23;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:49:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:43;s:11:"category_id";i:43;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"سنتافي";}s:11:" * original";a:4:{s:2:"id";i:43;s:11:"category_id";i:43;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"سنتافي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:45;s:9:"parent_id";i:39;s:4:"slug";s:4:"fyno";s:8:"position";i:24;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:08";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:45;s:9:"parent_id";i:39;s:4:"slug";s:4:"fyno";s:8:"position";i:24;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:08";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:45;s:11:"category_id";i:45;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"فينو";}s:11:" * original";a:4:{s:2:"id";i:45;s:11:"category_id";i:45;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"فينو";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:3;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:44;s:9:"parent_id";i:39;s:4:"slug";s:4:"kona";s:8:"position";i:25;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:21:56";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:44;s:9:"parent_id";i:39;s:4:"slug";s:4:"kona";s:8:"position";i:25;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:21:56";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:44;s:11:"category_id";i:44;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"كونا";}s:11:" * original";a:4:{s:2:"id";i:44;s:11:"category_id";i:44;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"كونا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:39;s:9:"parent_id";i:37;s:4:"slug";s:6:"honday";s:8:"position";i:21;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:47:15";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:39;s:11:"category_id";i:39;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"هونداي";}s:11:" * original";a:4:{s:2:"id";i:39;s:11:"category_id";i:39;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"هونداي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:38;s:9:"parent_id";i:37;s:4:"slug";s:3:"kya";s:8:"position";i:26;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:46:02";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:41;s:9:"parent_id";i:38;s:4:"slug";s:7:"sbortag";s:8:"position";i:27;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:46";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:41;s:9:"parent_id";i:38;s:4:"slug";s:7:"sbortag";s:8:"position";i:27;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:46";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:41;s:11:"category_id";i:41;s:6:"locale";s:5:"ar_PS";s:4:"name";s:14:"سبورتاج";}s:11:" * original";a:4:{s:2:"id";i:41;s:11:"category_id";i:41;s:6:"locale";s:5:"ar_PS";s:4:"name";s:14:"سبورتاج";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:40;s:9:"parent_id";i:38;s:4:"slug";s:7:"sorynto";s:8:"position";i:28;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:30";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:40;s:9:"parent_id";i:38;s:4:"slug";s:7:"sorynto";s:8:"position";i:28;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:48:30";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:40;s:11:"category_id";i:40;s:6:"locale";s:5:"ar_PS";s:4:"name";s:14:"سورينتو";}s:11:" * original";a:4:{s:2:"id";i:40;s:11:"category_id";i:40;s:6:"locale";s:5:"ar_PS";s:4:"name";s:14:"سورينتو";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:38;s:9:"parent_id";i:37;s:4:"slug";s:3:"kya";s:8:"position";i:26;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:46:02";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:38;s:11:"category_id";i:38;s:6:"locale";s:5:"ar_PS";s:4:"name";s:6:"كيا";}s:11:" * original";a:4:{s:2:"id";i:38;s:11:"category_id";i:38;s:6:"locale";s:5:"ar_PS";s:4:"name";s:6:"كيا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:63;s:9:"parent_id";i:37;s:4:"slug";s:14:"skoda-BrrvRxX9";s:8:"position";i:29;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 21:03:12";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:63;s:9:"parent_id";i:37;s:4:"slug";s:14:"skoda-BrrvRxX9";s:8:"position";i:29;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 21:03:12";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:63;s:11:"category_id";i:63;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"سكودا";}s:11:" * original";a:4:{s:2:"id";i:63;s:11:"category_id";i:63;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"سكودا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:3;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:66;s:9:"parent_id";i:37;s:4:"slug";s:4:"dmks";s:8:"position";i:30;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:38";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:66;s:9:"parent_id";i:37;s:4:"slug";s:4:"dmks";s:8:"position";i:30;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:38";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:66;s:11:"category_id";i:66;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"دمكس";}s:11:" * original";a:4:{s:2:"id";i:66;s:11:"category_id";i:66;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"دمكس";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:4;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:46;s:9:"parent_id";i:37;s:4:"slug";s:9:"mtsobyshy";s:8:"position";i:31;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:26";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:68;s:9:"parent_id";i:46;s:4:"slug";s:11:"hntr-tirton";s:8:"position";i:32;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 18:16:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:68;s:9:"parent_id";i:46;s:4:"slug";s:11:"hntr-tirton";s:8:"position";i:32;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 18:16:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:68;s:11:"category_id";i:68;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"هنتر- TIRTON";}s:11:" * original";a:4:{s:2:"id";i:68;s:11:"category_id";i:68;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"هنتر- TIRTON";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:47;s:9:"parent_id";i:46;s:4:"slug";s:9:"aot-landr";s:8:"position";i:33;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:37";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:47;s:9:"parent_id";i:46;s:4:"slug";s:9:"aot-landr";s:8:"position";i:33;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:37";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:47;s:11:"category_id";i:47;s:6:"locale";s:5:"ar_PS";s:4:"name";s:17:"اوت لاندر";}s:11:" * original";a:4:{s:2:"id";i:47;s:11:"category_id";i:47;s:6:"locale";s:5:"ar_PS";s:4:"name";s:17:"اوت لاندر";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:46;s:9:"parent_id";i:37;s:4:"slug";s:9:"mtsobyshy";s:8:"position";i:31;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 20:22:26";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:46;s:11:"category_id";i:46;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"متسوبيشي";}s:11:" * original";a:4:{s:2:"id";i:46;s:11:"category_id";i:46;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"متسوبيشي";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:5;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:51;s:9:"parent_id";i:37;s:4:"slug";s:6:"toyota";s:8:"position";i:34;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-23 20:52:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:65;s:9:"parent_id";i:51;s:4:"slug";s:11:"tndr-toyota";s:8:"position";i:35;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:21";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:65;s:9:"parent_id";i:51;s:4:"slug";s:11:"tndr-toyota";s:8:"position";i:35;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:21";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:65;s:11:"category_id";i:65;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"تندر تويوتا";}s:11:" * original";a:4:{s:2:"id";i:65;s:11:"category_id";i:65;s:6:"locale";s:5:"ar_PS";s:4:"name";s:21:"تندر تويوتا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:67;s:9:"parent_id";i:51;s:4:"slug";s:10:"land-krozr";s:8:"position";i:36;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:67;s:9:"parent_id";i:51;s:4:"slug";s:10:"land-krozr";s:8:"position";i:36;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-29 17:57:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:67;s:11:"category_id";i:67;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"لاند كروزر";}s:11:" * original";a:4:{s:2:"id";i:67;s:11:"category_id";i:67;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"لاند كروزر";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:51;s:9:"parent_id";i:37;s:4:"slug";s:6:"toyota";s:8:"position";i:34;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-23 20:52:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:51;s:11:"category_id";i:51;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"تويوتا";}s:11:" * original";a:4:{s:2:"id";i:51;s:11:"category_id";i:51;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"تويوتا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:6;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:52;s:9:"parent_id";i:37;s:4:"slug";s:5:"mazda";s:8:"position";i:37;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-23 20:53:10";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:52;s:9:"parent_id";i:37;s:4:"slug";s:5:"mazda";s:8:"position";i:37;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-23 20:53:10";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:52;s:11:"category_id";i:52;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"مازدا";}s:11:" * original";a:4:{s:2:"id";i:52;s:11:"category_id";i:52;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"مازدا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:37;s:9:"parent_id";N;s:4:"slug";s:33:"taadylat-oadafat-llgybat-oaltnadr";s:8:"position";i:20;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:45:41";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:37;s:11:"category_id";i:37;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:11:" * original";a:4:{s:2:"id";i:37;s:11:"category_id";i:37;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:8;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:53;s:9:"parent_id";N;s:4:"slug";s:22:"taadylat-ryady-lsyarat";s:8:"position";i:38;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:56;s:9:"parent_id";i:53;s:4:"slug";s:4:"syat";s:8:"position";i:39;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:27:46";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:61;s:9:"parent_id";i:56;s:4:"slug";s:4:"lyon";s:8:"position";i:40;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:32:34";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:61;s:9:"parent_id";i:56;s:4:"slug";s:4:"lyon";s:8:"position";i:40;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:32:34";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:61;s:11:"category_id";i:61;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"ليون";}s:11:" * original";a:4:{s:2:"id";i:61;s:11:"category_id";i:61;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"ليون";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:62;s:9:"parent_id";i:56;s:4:"slug";s:5:"abyza";s:8:"position";i:41;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:32:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:62;s:9:"parent_id";i:56;s:4:"slug";s:5:"abyza";s:8:"position";i:41;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:32:52";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:62;s:11:"category_id";i:62;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"ابيزا";}s:11:" * original";a:4:{s:2:"id";i:62;s:11:"category_id";i:62;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"ابيزا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:56;s:9:"parent_id";i:53;s:4:"slug";s:4:"syat";s:8:"position";i:39;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:27:46";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:56;s:11:"category_id";i:56;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"سيات";}s:11:" * original";a:4:{s:2:"id";i:56;s:11:"category_id";i:56;s:6:"locale";s:5:"ar_PS";s:4:"name";s:8:"سيات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:55;s:9:"parent_id";i:53;s:4:"slug";s:10:"folks-fagn";s:8:"position";i:42;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:27:28";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:59;s:9:"parent_id";i:55;s:4:"slug";s:8:"mk7-mk75";s:8:"position";i:43;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:29:02";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:59;s:9:"parent_id";i:55;s:4:"slug";s:8:"mk7-mk75";s:8:"position";i:43;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:29:02";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:59;s:11:"category_id";i:59;s:6:"locale";s:5:"ar_PS";s:4:"name";s:11:"MK7 & MK7.5";}s:11:" * original";a:4:{s:2:"id";i:59;s:11:"category_id";i:59;s:6:"locale";s:5:"ar_PS";s:4:"name";s:11:"MK7 & MK7.5";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:60;s:9:"parent_id";i:55;s:4:"slug";s:3:"mk8";s:8:"position";i:44;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:29:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:60;s:9:"parent_id";i:55;s:4:"slug";s:3:"mk8";s:8:"position";i:44;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:29:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:60;s:11:"category_id";i:60;s:6:"locale";s:5:"ar_PS";s:4:"name";s:3:"MK8";}s:11:" * original";a:4:{s:2:"id";i:60;s:11:"category_id";i:60;s:6:"locale";s:5:"ar_PS";s:4:"name";s:3:"MK8";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:64;s:9:"parent_id";i:55;s:4:"slug";s:5:"basat";s:8:"position";i:45;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-28 19:35:54";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:64;s:9:"parent_id";i:55;s:4:"slug";s:5:"basat";s:8:"position";i:45;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-28 19:35:54";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:64;s:11:"category_id";i:64;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"باسات";}s:11:" * original";a:4:{s:2:"id";i:64;s:11:"category_id";i:64;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"باسات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:55;s:9:"parent_id";i:53;s:4:"slug";s:10:"folks-fagn";s:8:"position";i:42;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:27:28";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:55;s:11:"category_id";i:55;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"فولكس فاجن";}s:11:" * original";a:4:{s:2:"id";i:55;s:11:"category_id";i:55;s:6:"locale";s:5:"ar_PS";s:4:"name";s:19:"فولكس فاجن";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:2;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:54;s:9:"parent_id";i:53;s:4:"slug";s:5:"skoda";s:8:"position";i:46;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:58;s:9:"parent_id";i:54;s:4:"slug";s:6:"sobyrb";s:8:"position";i:47;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:28:23";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:58;s:9:"parent_id";i:54;s:4:"slug";s:6:"sobyrb";s:8:"position";i:47;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:28:23";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:58;s:11:"category_id";i:58;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"سوبيرب";}s:11:" * original";a:4:{s:2:"id";i:58;s:11:"category_id";i:58;s:6:"locale";s:5:"ar_PS";s:4:"name";s:12:"سوبيرب";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}i:1;O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:57;s:9:"parent_id";i:54;s:4:"slug";s:8:"aoktafya";s:8:"position";i:48;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:28:09";s:10:"updated_at";s:19:"2025-06-30 18:59:37";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:57;s:9:"parent_id";i:54;s:4:"slug";s:8:"aoktafya";s:8:"position";i:48;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:28:09";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:57;s:11:"category_id";i:57;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"اوكتافيا";}s:11:" * original";a:4:{s:2:"id";i:57;s:11:"category_id";i:57;s:6:"locale";s:5:"ar_PS";s:4:"name";s:16:"اوكتافيا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:54;s:9:"parent_id";i:53;s:4:"slug";s:5:"skoda";s:8:"position";i:46;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:57";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:54;s:11:"category_id";i:54;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"سكودا";}s:11:" * original";a:4:{s:2:"id";i:54;s:11:"category_id";i:54;s:6:"locale";s:5:"ar_PS";s:4:"name";s:10:"سكودا";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:8:{s:2:"id";i:53;s:9:"parent_id";N;s:4:"slug";s:22:"taadylat-ryady-lsyarat";s:8:"position";i:38;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:53;s:11:"category_id";i:53;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:11:" * original";a:4:{s:2:"id";i:53;s:11:"category_id";i:53;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;s:8:" * total";i:49;s:15:" * parentColumn";s:9:"parent_id";s:33:" * removeItemsWithMissingAncestor";b:1;s:14:" * indentChars";s:8:"    ";s:15:" * childrenName";s:5:"items";s:17:" * parentRelation";s:6:"parent";}i:1;a:1:{s:27:"_fleetcart_cache_categories";s:27:"_fleetcart_cache_categories";}i:2;i:1783448355;}