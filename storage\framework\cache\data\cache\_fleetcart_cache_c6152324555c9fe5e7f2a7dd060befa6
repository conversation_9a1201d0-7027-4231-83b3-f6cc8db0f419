a:3:{i:0;O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:8:{i:0;a:2:{s:3:"url";s:46:"http://cars.test/ar_PS/brands/citroen/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:35;s:7:"user_id";i:1;s:8:"filename";s:16:"ستروين.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/zD9KwZb0wR6AiYkRU1Y3fZVKBsEBUHFbkNsy41pS.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17035";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";}s:11:" * original";a:17:{s:2:"id";i:35;s:7:"user_id";i:1;s:8:"filename";s:16:"ستروين.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/zD9KwZb0wR6AiYkRU1Y3fZVKBsEBUHFbkNsy41pS.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17035";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";s:15:"pivot_entity_id";i:7;s:13:"pivot_file_id";i:35;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:87;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 02:07:31";s:16:"pivot_updated_at";s:19:"2025-03-02 02:07:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:7;s:7:"file_id";i:35;s:2:"id";i:87;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:07:31";s:10:"updated_at";s:19:"2025-03-02 02:07:31";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:7;s:7:"file_id";i:35;s:2:"id";i:87;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:07:31";s:10:"updated_at";s:19:"2025-03-02 02:07:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:28:"Modules\Brand\Entities\Brand":33:{s:13:" * connection";N;s:8:" * table";s:6:"brands";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:4:"slug";i:1;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:"translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:1;a:2:{s:3:"url";s:46:"http://cars.test/ar_PS/brands/hyundai/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:41;s:7:"user_id";i:1;s:8:"filename";s:23:"هنداي اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Hb6dmAB2BScqkqwsAOZNtntBZDaqXZkn01lmAMfo.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17694";s:10:"created_at";s:19:"2025-03-02 01:57:21";s:10:"updated_at";s:19:"2025-03-02 01:57:21";}s:11:" * original";a:17:{s:2:"id";i:41;s:7:"user_id";i:1;s:8:"filename";s:23:"هنداي اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Hb6dmAB2BScqkqwsAOZNtntBZDaqXZkn01lmAMfo.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17694";s:10:"created_at";s:19:"2025-03-02 01:57:21";s:10:"updated_at";s:19:"2025-03-02 01:57:21";s:15:"pivot_entity_id";i:3;s:13:"pivot_file_id";i:41;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:79;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 01:58:50";s:16:"pivot_updated_at";s:19:"2025-03-02 01:58:50";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:3;s:7:"file_id";i:41;s:2:"id";i:79;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:58:50";s:10:"updated_at";s:19:"2025-03-02 01:58:50";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:3;s:7:"file_id";i:41;s:2:"id";i:79;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:58:50";s:10:"updated_at";s:19:"2025-03-02 01:58:50";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:2;a:2:{s:3:"url";s:42:"http://cars.test/ar_PS/brands/kia/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:37;s:7:"user_id";i:1;s:8:"filename";s:19:"كيا اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/AXOI1gFJfDbt8AW0jjkuGnsrvX20fagWsHD9uog7.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"10216";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";}s:11:" * original";a:17:{s:2:"id";i:37;s:7:"user_id";i:1;s:8:"filename";s:19:"كيا اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/AXOI1gFJfDbt8AW0jjkuGnsrvX20fagWsHD9uog7.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"10216";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";s:15:"pivot_entity_id";i:6;s:13:"pivot_file_id";i:37;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:85;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 02:05:20";s:16:"pivot_updated_at";s:19:"2025-03-02 02:05:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:6;s:7:"file_id";i:37;s:2:"id";i:85;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:05:20";s:10:"updated_at";s:19:"2025-03-02 02:05:20";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:6;s:7:"file_id";i:37;s:2:"id";i:85;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:05:20";s:10:"updated_at";s:19:"2025-03-02 02:05:20";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:3;a:2:{s:3:"url";s:44:"http://cars.test/ar_PS/brands/mazda/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:38;s:7:"user_id";i:1;s:8:"filename";s:14:"مازدا.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/I5SKlZWSwg8FmjTEgoxfb3uPxLmS3T70RmwOpzKW.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"21234";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";}s:11:" * original";a:17:{s:2:"id";i:38;s:7:"user_id";i:1;s:8:"filename";s:14:"مازدا.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/I5SKlZWSwg8FmjTEgoxfb3uPxLmS3T70RmwOpzKW.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"21234";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";s:15:"pivot_entity_id";i:5;s:13:"pivot_file_id";i:38;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:83;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 02:04:33";s:16:"pivot_updated_at";s:19:"2025-03-02 02:04:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:5;s:7:"file_id";i:38;s:2:"id";i:83;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:04:33";s:10:"updated_at";s:19:"2025-03-02 02:04:33";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:5;s:7:"file_id";i:38;s:2:"id";i:83;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 02:04:33";s:10:"updated_at";s:19:"2025-03-02 02:04:33";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:4;a:2:{s:3:"url";s:47:"http://cars.test/ar_PS/brands/mercedes/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:40;s:7:"user_id";i:1;s:8:"filename";s:25:"مرسيدس اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/wVzMq8pqlvzk48NJxygztNlN3uU6GhpeH2d4oqPO.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"20763";s:10:"created_at";s:19:"2025-03-02 01:57:21";s:10:"updated_at";s:19:"2025-03-02 01:57:21";}s:11:" * original";a:17:{s:2:"id";i:40;s:7:"user_id";i:1;s:8:"filename";s:25:"مرسيدس اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/wVzMq8pqlvzk48NJxygztNlN3uU6GhpeH2d4oqPO.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"20763";s:10:"created_at";s:19:"2025-03-02 01:57:21";s:10:"updated_at";s:19:"2025-03-02 01:57:21";s:15:"pivot_entity_id";i:4;s:13:"pivot_file_id";i:40;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:81;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 01:59:31";s:16:"pivot_updated_at";s:19:"2025-03-02 01:59:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:4;s:7:"file_id";i:40;s:2:"id";i:81;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:59:31";s:10:"updated_at";s:19:"2025-03-02 01:59:31";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:4;s:7:"file_id";i:40;s:2:"id";i:81;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:59:31";s:10:"updated_at";s:19:"2025-03-02 01:59:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:5;a:2:{s:3:"url";s:44:"http://cars.test/ar_PS/brands/skoda/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:36;s:7:"user_id";i:1;s:8:"filename";s:23:"سكودا اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/GxK8IQk2CMlh06luDZ3Z6hVTWL1WPy6CJ9FbmFKI.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"21797";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";}s:11:" * original";a:17:{s:2:"id";i:36;s:7:"user_id";i:1;s:8:"filename";s:23:"سكودا اصفر.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/GxK8IQk2CMlh06luDZ3Z6hVTWL1WPy6CJ9FbmFKI.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"21797";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";s:15:"pivot_entity_id";i:1;s:13:"pivot_file_id";i:36;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:75;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 01:57:50";s:16:"pivot_updated_at";s:19:"2025-03-02 01:57:50";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:1;s:7:"file_id";i:36;s:2:"id";i:75;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:57:50";s:10:"updated_at";s:19:"2025-03-02 01:57:50";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:1;s:7:"file_id";i:36;s:2:"id";i:75;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:57:50";s:10:"updated_at";s:19:"2025-03-02 01:57:50";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:6;a:2:{s:3:"url";s:45:"http://cars.test/ar_PS/brands/toyota/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:33;s:7:"user_id";i:1;s:8:"filename";s:16:"تويوتا.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/vatJ7XHlyivjv2Gj1iD8Hq4GfHmz4fyJ5JGGhAOd.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"20281";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";}s:11:" * original";a:17:{s:2:"id";i:33;s:7:"user_id";i:1;s:8:"filename";s:16:"تويوتا.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/vatJ7XHlyivjv2Gj1iD8Hq4GfHmz4fyJ5JGGhAOd.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"20281";s:10:"created_at";s:19:"2025-03-02 01:57:20";s:10:"updated_at";s:19:"2025-03-02 01:57:20";s:15:"pivot_entity_id";i:8;s:13:"pivot_file_id";i:33;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:923;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-06-10 01:17:41";s:16:"pivot_updated_at";s:19:"2025-06-10 01:17:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:8;s:7:"file_id";i:33;s:2:"id";i:923;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-06-10 01:17:41";s:10:"updated_at";s:19:"2025-06-10 01:17:41";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:8;s:7:"file_id";i:33;s:2:"id";i:923;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-06-10 01:17:41";s:10:"updated_at";s:19:"2025-06-10 01:17:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}i:7;a:2:{s:3:"url";s:41:"http://cars.test/ar_PS/brands/vw/products";s:4:"logo";O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:31;s:7:"user_id";i:1;s:8:"filename";s:6:"vw.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/vjpK8JsSkV0SY7YmGHAXyNLOofTgYFsmraSPIUTo.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17398";s:10:"created_at";s:19:"2025-03-02 01:57:19";s:10:"updated_at";s:19:"2025-03-02 01:57:19";}s:11:" * original";a:17:{s:2:"id";i:31;s:7:"user_id";i:1;s:8:"filename";s:6:"vw.jpg";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/vjpK8JsSkV0SY7YmGHAXyNLOofTgYFsmraSPIUTo.jpg";s:9:"extension";s:3:"jpg";s:4:"mime";s:10:"image/jpeg";s:4:"size";s:5:"17398";s:10:"created_at";s:19:"2025-03-02 01:57:19";s:10:"updated_at";s:19:"2025-03-02 01:57:19";s:15:"pivot_entity_id";i:2;s:13:"pivot_file_id";i:31;s:17:"pivot_entity_type";s:28:"Modules\Brand\Entities\Brand";s:8:"pivot_id";i:77;s:10:"pivot_zone";s:4:"logo";s:16:"pivot_created_at";s:19:"2025-03-02 01:58:29";s:16:"pivot_updated_at";s:19:"2025-03-02 01:58:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";N;s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:2;s:7:"file_id";i:31;s:2:"id";i:77;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:58:29";s:10:"updated_at";s:19:"2025-03-02 01:58:29";}s:11:" * original";a:7:{s:11:"entity_type";s:28:"Modules\Brand\Entities\Brand";s:9:"entity_id";i:2;s:7:"file_id";i:31;s:2:"id";i:77;s:4:"zone";s:4:"logo";s:10:"created_at";s:19:"2025-03-02 01:58:29";s:10:"updated_at";s:19:"2025-03-02 01:58:29";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:102;s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:28:"Modules\Brand\Entities\Brand";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}}s:28:" * escapeWhenCastingToString";b:0;}i:1;a:0:{}i:2;i:1783449982;}