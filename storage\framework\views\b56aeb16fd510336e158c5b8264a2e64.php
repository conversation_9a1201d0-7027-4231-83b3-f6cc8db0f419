<?php $__env->startSection('breadcrumb'); ?>
    <?php if(request()->routeIs('account.dashboard.index')): ?>
        <li class="active"><?php echo e(trans('storefront::account.pages.my_account')); ?></li>
    <?php else: ?>
        <li><a href="<?php echo e(route('account.dashboard.index')); ?>"><?php echo e(trans('storefront::account.pages.my_account')); ?></a></li>
    <?php endif; ?>

    <?php echo $__env->yieldContent('account_breadcrumb'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <section class="account-wrap">
        <div class="container">
            <div class="account-wrap-inner">
                <div class="account-left">
                    <ul class="account-sidebar list-inline d-flex flex-column">
                        <li class="<?php echo e(request()->routeIs('account.dashboard.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.dashboard.index')); ?>">
                                <i class="las la-tachometer-alt"></i>

                                <?php echo e(trans('storefront::account.pages.dashboard')); ?>

                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.orders.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.orders.index')); ?>">
                                <i class="las la-cart-arrow-down"></i>

                                <?php echo e(trans('storefront::account.pages.my_orders')); ?>

                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.downloads.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.downloads.index')); ?>">
                                <i class="las la-download"></i>

                                <?php echo e(trans('storefront::account.pages.my_downloads')); ?>

                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.wishlist.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.wishlist.index')); ?>">
                                <i class="lar la-heart"></i>

                                <?php echo e(trans('storefront::account.pages.my_wishlist')); ?>


                                <span class="count" x-text="$store.state.wishlistCount"></span>
                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.reviews.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.reviews.index')); ?>">
                                <i class="las la-comment"></i>

                                <?php echo e(trans('storefront::account.pages.my_reviews')); ?>

                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.addresses.index') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.addresses.index')); ?>">
                                <i class="las la-address-book"></i>

                                <?php echo e(trans('storefront::account.pages.my_addresses')); ?>

                            </a>
                        </li>

                        <li class="<?php echo e(request()->routeIs('account.profile.edit') ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('account.profile.edit')); ?>">
                                <i class="las la-user-circle"></i>

                                <?php echo e(trans('storefront::account.pages.my_profile')); ?>

                            </a>
                        </li>

                        <li>
                            <a href="<?php echo e(route('logout')); ?>">
                                <i class="las la-sign-out-alt"></i>

                                <?php echo e(trans('storefront::account.pages.logout')); ?>

                            </a>
                        </li>
                    </ul>
                </div>

                <div class="account-right">
                    <div class="panel-wrap">
                        <?php echo $__env->yieldContent('panel'); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('globals'); ?>
    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Storefront/Resources/assets/public/sass/pages/account/main.scss'
    ]); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('storefront::public.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/account/layout.blade.php ENDPATH**/ ?>