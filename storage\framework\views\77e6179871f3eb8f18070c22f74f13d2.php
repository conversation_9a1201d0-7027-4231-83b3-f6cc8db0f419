<div class="form-group variant-input">
    <div class="row">
        <div class="col-lg-18">
            <label for="option-<?php echo e($option->id); ?>">
                <?php echo $option->name . ($option->is_required ? '<span>*</span>' : ''); ?>

            </label>
        </div>

        <div class="col-lg-18">
            <div class="form-input">
                <input
                    name="options[<?php echo e($option->id); ?>]"
                    class="form-control <?php echo e(array_pull($attributes, 'class')); ?>"
                    id="option-<?php echo e($option->id); ?>"
                    x-model="cartItemForm.options[<?php echo e($option->id); ?>]"
                    <?php echo e(html_attrs($attributes)); ?>

                >
            </div>

            <template x-if="errors.has('<?php echo e("options.{$option->id}"); ?>')">
                <span class="error-message" x-text="errors.get('<?php echo e("options.{$option->id}"); ?>')"></span>
            </template>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/custom_options/input.blade.php ENDPATH**/ ?>