<div class="social-share">
    <label><?php echo e(trans('storefront::product.share')); ?></label>

    <ul class="list-inline social-links d-flex">
        <li>
            <a
                :href="`https://www.facebook.com/sharer/sharer.php?u=${productUrl}`"
                title="<?php echo e(trans('storefront::product.facebook')); ?>"
                target="_blank"
            >
                <i class="lab la-facebook"></i>
            </a>
        </li>

        <li>
            <a
                :href="`https://api.whatsapp.com/send?text=<?php echo e($product->name); ?> ${productUrl}`"
                title="WhatsApp"
                target="_blank"
            >
                <i class="lab la-whatsapp"></i>
            </a>
        </li>

        <li>
            <a
                href="javascript:void(0)"
                title="Copy URL"
                onclick="copyProductUrl()"
            >
                <i class="las la-link"></i>
            </a>
        </li>
    </ul>
</div>

<!-- Toast notification for copy -->
<div id="copy-toast" style="position: fixed; bottom: 20px; right: 20px; background-color: #333; color: white; padding: 10px 20px; border-radius: 4px; display: none; z-index: 9999; font-size: 14px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
    <span>URL Copied!</span>
</div>

<script>
function copyProductUrl() {
    const productUrl = '<?php echo e($product->url()); ?>';
    
    navigator.clipboard.writeText(productUrl)
        .then(() => {
            // Show toast notification
            const toast = document.getElementById('copy-toast');
            toast.style.display = 'block';
            
            // Hide after 2 seconds
            setTimeout(() => {
                toast.style.display = 'none';
            }, 2000);
        })
        .catch(err => {
            console.error('Could not copy URL: ', err);
        });
}
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Storefront/Resources/views/public/products/show/social_share.blade.php ENDPATH**/ ?>