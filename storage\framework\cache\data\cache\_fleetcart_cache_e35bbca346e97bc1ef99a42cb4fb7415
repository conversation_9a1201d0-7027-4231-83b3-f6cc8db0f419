a:3:{i:0;O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:9:{i:0;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:10;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:19;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:17:"las la-grip-lines";s:6:"target";s:5:"_self";s:8:"position";i:0;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:09";s:10:"updated_at";s:19:"2025-06-30 20:16:35";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:10;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:19;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:17:"las la-grip-lines";s:6:"target";s:5:"_self";s:8:"position";i:0;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:09";s:10:"updated_at";s:19:"2025-06-30 20:16:35";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:14;s:12:"menu_item_id";i:10;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:11:" * original";a:4:{s:2:"id";i:14;s:12:"menu_item_id";i:10;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";}s:11:" * original";a:8:{s:2:"id";i:19;s:9:"parent_id";N;s:4:"slug";s:13:"ardyat-alsyar";s:8:"position";i:0;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 16:28:58";s:10:"updated_at";s:19:"2025-07-02 02:08:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:11:" * original";a:4:{s:2:"id";i:19;s:11:"category_id";i:19;s:6:"locale";s:5:"ar_PS";s:4:"name";s:27:"ارضيات السيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:1;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:15;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:15;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-chair";s:6:"target";s:5:"_self";s:8:"position";i:1;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:20:21";s:10:"updated_at";s:19:"2025-06-30 20:01:13";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:15;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:15;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-chair";s:6:"target";s:5:"_self";s:8:"position";i:1;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:20:21";s:10:"updated_at";s:19:"2025-06-30 20:01:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:19;s:12:"menu_item_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:11:" * original";a:4:{s:2:"id";i:19;s:12:"menu_item_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:15;s:9:"parent_id";N;s:4:"slug";s:22:"frsh-almkaaad-omlhkath";s:8:"position";i:3;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:38:40";s:10:"updated_at";s:19:"2025-07-02 02:12:25";}s:11:" * original";a:8:{s:2:"id";i:15;s:9:"parent_id";N;s:4:"slug";s:22:"frsh-almkaaad-omlhkath";s:8:"position";i:3;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 14:38:40";s:10:"updated_at";s:19:"2025-07-02 02:12:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:15;s:11:"category_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:11:" * original";a:4:{s:2:"id";i:15;s:11:"category_id";i:15;s:6:"locale";s:5:"ar_PS";s:4:"name";s:38:"فرش المقاعد وملحقاته";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:2;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:12;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:25;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:21:"las la-tachometer-alt";s:6:"target";s:5:"_self";s:8:"position";i:2;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:58";s:10:"updated_at";s:19:"2025-06-30 20:15:15";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:12;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:25;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:21:"las la-tachometer-alt";s:6:"target";s:5:"_self";s:8:"position";i:2;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:58";s:10:"updated_at";s:19:"2025-06-30 20:15:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:16;s:12:"menu_item_id";i:12;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"اكسسوارات داخلية";}s:11:" * original";a:4:{s:2:"id";i:16;s:12:"menu_item_id";i:12;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"اكسسوارات داخلية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";}s:11:" * original";a:8:{s:2:"id";i:25;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-dakhly-mtnoaa";s:8:"position";i:7;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 17:42:44";s:10:"updated_at";s:19:"2025-07-02 02:14:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:11:" * original";a:4:{s:2:"id";i:25;s:11:"category_id";i:25;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات داخلية متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:3;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:11;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:35;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:15:"las la-car-side";s:6:"target";s:5:"_self";s:8:"position";i:3;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:41";s:10:"updated_at";s:19:"2025-06-30 20:15:03";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:11;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:35;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:15:"las la-car-side";s:6:"target";s:5:"_self";s:8:"position";i:3;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:44:41";s:10:"updated_at";s:19:"2025-06-30 20:15:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:15;s:12:"menu_item_id";i:11;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"اكسسوارات خارجية";}s:11:" * original";a:4:{s:2:"id";i:15;s:12:"menu_item_id";i:11;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"اكسسوارات خارجية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:35;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-khargy-mtnoaa";s:8:"position";i:12;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 03:14:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:11:" * original";a:8:{s:2:"id";i:35;s:9:"parent_id";N;s:4:"slug";s:23:"akssoarat-khargy-mtnoaa";s:8:"position";i:12;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 03:14:17";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:35;s:11:"category_id";i:35;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات خارجية متنوعة";}s:11:" * original";a:4:{s:2:"id";i:35;s:11:"category_id";i:35;s:6:"locale";s:5:"ar_PS";s:4:"name";s:44:"اكسسوارات خارجية متنوعة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:27:"Modules\Media\Entities\File":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:158;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٠.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Am4TBPMIlKGiVFfi8I4cAJzwYpKXXpsdzFuHeswf.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:7:"1872642";s:10:"created_at";s:19:"2025-06-08 18:14:20";s:10:"updated_at";s:19:"2025-06-08 18:14:20";}s:11:" * original";a:17:{s:2:"id";i:158;s:7:"user_id";i:1;s:8:"filename";s:21:"الملف_٠٠٠.png";s:4:"disk";s:14:"public_storage";s:4:"path";s:50:"media/Am4TBPMIlKGiVFfi8I4cAJzwYpKXXpsdzFuHeswf.png";s:9:"extension";s:3:"png";s:4:"mime";s:9:"image/png";s:4:"size";s:7:"1872642";s:10:"created_at";s:19:"2025-06-08 18:14:20";s:10:"updated_at";s:19:"2025-06-08 18:14:20";s:15:"pivot_entity_id";i:11;s:13:"pivot_file_id";i:158;s:17:"pivot_entity_type";s:30:"Modules\Menu\Entities\MenuItem";s:8:"pivot_id";i:1762;s:10:"pivot_zone";s:16:"background_image";s:16:"pivot_created_at";s:19:"2025-06-30 20:15:03";s:16:"pivot_updated_at";s:19:"2025-06-30 20:15:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:49:"Illuminate\Database\Eloquent\Relations\MorphPivot":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:12:"entity_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:11:"entity_type";s:30:"Modules\Menu\Entities\MenuItem";s:9:"entity_id";i:11;s:7:"file_id";i:158;s:2:"id";i:1762;s:4:"zone";s:16:"background_image";s:10:"created_at";s:19:"2025-06-30 20:15:03";s:10:"updated_at";s:19:"2025-06-30 20:15:03";}s:11:" * original";a:7:{s:11:"entity_type";s:30:"Modules\Menu\Entities\MenuItem";s:9:"entity_id";i:11;s:7:"file_id";i:158;s:2:"id";i:1762;s:4:"zone";s:16:"background_image";s:10:"created_at";s:19:"2025-06-30 20:15:03";s:10:"updated_at";s:19:"2025-06-30 20:15:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:13:" * foreignKey";s:9:"entity_id";s:13:" * relatedKey";s:7:"file_id";s:12:" * morphType";s:11:"entity_type";s:13:" * morphClass";s:30:"Modules\Menu\Entities\MenuItem";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:3:{i:0;s:2:"id";i:1;s:8:"filename";i:2;s:4:"path";}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:4;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:13;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:29;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:16:"las la-lightbulb";s:6:"target";s:5:"_self";s:8:"position";i:4;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:45:24";s:10:"updated_at";s:19:"2025-06-30 20:14:51";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:13;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:29;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:16:"las la-lightbulb";s:6:"target";s:5:"_self";s:8:"position";i:4;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 21:45:24";s:10:"updated_at";s:19:"2025-06-30 20:14:51";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:17;s:12:"menu_item_id";i:13;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:11:" * original";a:4:{s:2:"id";i:17;s:12:"menu_item_id";i:13;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:29;s:9:"parent_id";N;s:4:"slug";s:24:"alanar-oalaghz-alkhrbayy";s:8:"position";i:16;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:14";s:10:"updated_at";s:19:"2025-06-30 19:01:18";}s:11:" * original";a:8:{s:2:"id";i:29;s:9:"parent_id";N;s:4:"slug";s:24:"alanar-oalaghz-alkhrbayy";s:8:"position";i:16;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-08 18:03:14";s:10:"updated_at";s:19:"2025-06-30 19:01:18";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:29;s:11:"category_id";i:29;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:11:" * original";a:4:{s:2:"id";i:29;s:11:"category_id";i:29;s:6:"locale";s:5:"ar_PS";s:4:"name";s:52:"الانارة والاجهزة الكهربائية";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:5;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:14;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:32;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-brush";s:6:"target";s:5:"_self";s:8:"position";i:5;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:19:30";s:10:"updated_at";s:19:"2025-06-30 19:51:26";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:14;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:32;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-brush";s:6:"target";s:5:"_self";s:8:"position";i:5;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:19:30";s:10:"updated_at";s:19:"2025-06-30 19:51:26";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:18;s:12:"menu_item_id";i:14;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:11:" * original";a:4:{s:2:"id";i:18;s:12:"menu_item_id";i:14;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:32;s:9:"parent_id";N;s:4:"slug";s:15:"alaanay-balmrkb";s:8:"position";i:13;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:29:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:11:" * original";a:8:{s:2:"id";i:32;s:9:"parent_id";N;s:4:"slug";s:15:"alaanay-balmrkb";s:8:"position";i:13;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-09 02:29:16";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:32;s:11:"category_id";i:32;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:11:" * original";a:4:{s:2:"id";i:32;s:11:"category_id";i:32;s:6:"locale";s:5:"ar_PS";s:4:"name";s:31:"العناية بالمركبة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:6;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:16;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:36;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-tools";s:6:"target";s:5:"_self";s:8:"position";i:6;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:20:51";s:10:"updated_at";s:19:"2025-06-30 20:14:30";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:16;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:36;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:12:"las la-tools";s:6:"target";s:5:"_self";s:8:"position";i:6;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:20:51";s:10:"updated_at";s:19:"2025-06-30 20:14:30";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:20;s:12:"menu_item_id";i:16;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:11:" * original";a:4:{s:2:"id";i:20;s:12:"menu_item_id";i:16;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:36;s:9:"parent_id";N;s:4:"slug";s:19:"maadat-oadoat-lsyar";s:8:"position";i:11;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:38:51";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:11:" * original";a:8:{s:2:"id";i:36;s:9:"parent_id";N;s:4:"slug";s:19:"maadat-oadoat-lsyar";s:8:"position";i:11;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:38:51";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:36;s:11:"category_id";i:36;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:11:" * original";a:4:{s:2:"id";i:36;s:11:"category_id";i:36;s:6:"locale";s:5:"ar_PS";s:4:"name";s:36:"معدات وادوات لسيارة";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:7;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:17;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:37;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:20:"las la-truck-monster";s:6:"target";s:5:"_self";s:8:"position";i:7;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:21:17";s:10:"updated_at";s:19:"2025-06-30 20:14:17";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:17;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:37;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:20:"las la-truck-monster";s:6:"target";s:5:"_self";s:8:"position";i:7;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:21:17";s:10:"updated_at";s:19:"2025-06-30 20:14:17";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:21;s:12:"menu_item_id";i:17;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:11:" * original";a:4:{s:2:"id";i:21;s:12:"menu_item_id";i:17;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:37;s:9:"parent_id";N;s:4:"slug";s:33:"taadylat-oadafat-llgybat-oaltnadr";s:8:"position";i:20;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:45:41";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:11:" * original";a:8:{s:2:"id";i:37;s:9:"parent_id";N;s:4:"slug";s:33:"taadylat-oadafat-llgybat-oaltnadr";s:8:"position";i:20;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-15 18:45:41";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:37;s:11:"category_id";i:37;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:11:" * original";a:4:{s:2:"id";i:37;s:11:"category_id";i:37;s:6:"locale";s:5:"ar_PS";s:4:"name";s:59:"تعديلات خارجية للجيبات والتنادر";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}i:8;O:26:"Modules\Menu\MegaMenu\Menu":2:{s:32:" Modules\Menu\MegaMenu\Menu menu";O:30:"Modules\Menu\Entities\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:16:{s:2:"id";i:18;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:53;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:21:"las la-flag-checkered";s:6:"target";s:5:"_self";s:8:"position";i:8;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:21:38";s:10:"updated_at";s:19:"2025-06-30 20:14:03";s:5:"items";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:11:" * original";a:15:{s:2:"id";i:18;s:7:"menu_id";i:2;s:9:"parent_id";i:7;s:11:"category_id";i:53;s:7:"page_id";N;s:4:"type";s:8:"category";s:3:"url";N;s:4:"icon";s:21:"las la-flag-checkered";s:6:"target";s:5:"_self";s:8:"position";i:8;s:7:"is_root";i:0;s:8:"is_fluid";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-30 19:21:38";s:10:"updated_at";s:19:"2025-06-30 20:14:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:7:"is_root";s:7:"boolean";s:8:"is_fluid";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:16:"background_image";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:4:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Modules\Menu\Entities\MenuItemTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"menu_item_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:22;s:12:"menu_item_id";i:18;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:11:" * original";a:4:{s:2:"id";i:22;s:12:"menu_item_id";i:18;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:8:"category";O:34:"Modules\Category\Entities\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:1:{i:0;s:12:"translations";}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:53;s:9:"parent_id";N;s:4:"slug";s:22:"taadylat-ryady-lsyarat";s:8:"position";i:38;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:11:" * original";a:8:{s:2:"id";i:53;s:9:"parent_id";N;s:4:"slug";s:22:"taadylat-ryady-lsyarat";s:8:"position";i:38;s:13:"is_searchable";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-06-26 02:26:18";s:10:"updated_at";s:19:"2025-06-30 18:59:37";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:13:"is_searchable";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"translations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:45:"Modules\Category\Entities\CategoryTranslation":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"category_translations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:53;s:11:"category_id";i:53;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:11:" * original";a:4:{s:2:"id";i:53;s:11:"category_id";i:53;s:6:"locale";s:5:"ar_PS";s:4:"name";s:42:"تعديلات رياضية لسيارات";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:1:{i:0;s:4:"name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:1:{i:0;s:12:"translations";}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"parent_id";i:1;s:4:"slug";i:2;s:8:"position";i:3;s:13:"is_searchable";i:4;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * slugAttribute";s:4:"name";s:16:" * defaultLocale";N;}s:4:"page";N;s:5:"files";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:12:{i:0;s:7:"menu_id";i:1;s:11:"category_id";i:2;s:7:"page_id";i:3;s:9:"parent_id";i:4;s:4:"type";i:5;s:3:"url";i:6;s:4:"icon";i:7;s:6:"target";i:8;s:8:"position";i:9;s:7:"is_root";i:10;s:8:"is_fluid";i:11;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:23:" * translatedAttributes";a:1:{i:0;s:4:"name";}s:16:" * defaultLocale";N;}s:36:" Modules\Menu\MegaMenu\Menu subMenus";N;}}s:28:" * escapeWhenCastingToString";b:0;}i:1;a:4:{s:26:"_fleetcart_cache_mega_menu";s:26:"_fleetcart_cache_mega_menu";s:27:"_fleetcart_cache_menu_items";s:27:"_fleetcart_cache_menu_items";s:22:"_fleetcart_cache_pages";s:22:"_fleetcart_cache_pages";s:27:"_fleetcart_cache_categories";s:27:"_fleetcart_cache_categories";}i:2;i:1783448003;}