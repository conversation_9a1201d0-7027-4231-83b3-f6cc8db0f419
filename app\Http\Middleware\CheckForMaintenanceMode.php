<?php

namespace FleetCart\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Foundation\Http\Exceptions\MaintenanceModeException;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode as BaseCheckForMaintenanceMode;

class CheckForMaintenanceMode extends BaseCheckForMaintenanceMode
{
    /**
     * The URIs that should be accessible while maintenance mode is enabled.
     *
     * @var array
     */
    protected $except = [
        '*/admin*',
        'admin*',
    ];


    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     *
     * @throws HttpException
     * @throws MaintenanceModeException
     */
    public function handle($request, Closure $next): mixed
    {
        // If app is not installed, let it proceed normally
        if (!config('app.installed')) {
            return parent::handle($request, $next);
        }

        // If app is not in maintenance mode, let everyone access
        if (!$this->app->isDownForMaintenance()) {
            return $next($request);
        }

        // Check if this is an admin route (should be excluded anyway)
        if ($this->inExceptArray($request)) {
            return $next($request);
        }

        // Check for admin bypass parameter
        if ($request->has('admin_bypass') && $request->get('admin_bypass') === 'true') {
            return $next($request);
        }

        // Check if current user is authenticated and is an admin
        $user = auth()->user();
        if ($user && $user->hasRoleName('Admin')) {
            return $next($request);
        }

        // Additional check: Look for admin session or cookie
        if ($this->isAdminAuthenticated($request)) {
            return $next($request);
        }

        // For everyone else during maintenance, show maintenance page
        return parent::handle($request, $next);
    }

    /**
     * Check if admin is authenticated through various methods
     */
    private function isAdminAuthenticated($request): bool
    {
        // Check if there's an admin session
        if (session()->has('admin_authenticated') || session()->has('admin_user_id')) {
            return true;
        }

        // Check for Laravel session with admin user
        $sessionId = $request->session()->getId();
        if ($sessionId && session()->has('login_web_' . sha1('web'))) {
            $userId = session()->get('login_web_' . sha1('web'));
            if ($userId) {
                try {
                    $user = \Modules\User\Entities\User::find($userId);
                    if ($user && $user->hasRoleName('Admin')) {
                        return true;
                    }
                } catch (\Exception $e) {
                    // Ignore errors and continue
                }
            }
        }

        // Check if request comes from admin panel referrer
        $referer = $request->header('referer');
        if ($referer && (strpos($referer, '/admin') !== false)) {
            return true;
        }

        return false;
    }
}
