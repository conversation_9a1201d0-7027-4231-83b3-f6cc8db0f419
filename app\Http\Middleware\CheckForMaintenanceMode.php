<?php

namespace FleetCart\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Foundation\Http\Exceptions\MaintenanceModeException;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode as BaseCheckForMaintenanceMode;

class CheckForMaintenanceMode extends BaseCheckForMaintenanceMode
{
    /**
     * The URIs that should be accessible while maintenance mode is enabled.
     *
     * @var array
     */
    protected $except = [
        '*/admin*',
    ];


    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     *
     * @throws HttpException
     * @throws MaintenanceModeException
     */
    public function handle($request, Closure $next): mixed
    {
        // If app is not installed, let it proceed normally
        if (!config('app.installed')) {
            return parent::handle($request, $next);
        }

        // If app is not in maintenance mode, let everyone access
        if (!$this->app->isDownForMaintenance()) {
            return $next($request);
        }

        // If app is in maintenance mode, only allow admins to access
        if (optional(auth()->user())->hasRoleName('Admin')) {
            return $next($request);
        }

        // For everyone else during maintenance, show maintenance page
        return parent::handle($request, $next);
    }
}
