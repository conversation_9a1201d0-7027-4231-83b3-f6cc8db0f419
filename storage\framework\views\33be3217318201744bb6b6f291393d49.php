<?php echo $__env->make('media::admin.image_picker.single', [
    'title' => trans('setting::settings.form.logo'),
    'inputName' => 'translatable[admin_logo]',
    'file' => $logo,
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<div class="media-picker-divider"></div>

<?php echo $__env->make('media::admin.image_picker.single', [
    'title' => trans('setting::settings.form.small_logo'),
    'inputName' => 'translatable[admin_small_logo]',
    'file' => $shortLogo,
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Setting/Resources/views/admin/settings/tabs/logo.blade.php ENDPATH**/ ?>