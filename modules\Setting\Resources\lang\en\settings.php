<?php

return [
    'settings' => 'Settings',
    'tabs' => [
        'group' => [
            'general_settings' => 'General Settings',
            'social_logins' => 'Social Logins',
            'shipping_methods' => 'Shipping Methods',
            'payment_methods' => 'Payment Methods',
        ],
        'general' => 'General',
        'logo' => 'Logo',
        'maintenance' => 'Maintenance',
        'store' => 'Store',
        'pwa' => 'PWA',
        'currency' => 'Currency',
        'sms' => 'SMS',
        'mail' => 'Mail',
        'newsletter' => 'Newsletter',
        'google_recaptcha' => 'Google reCAPTCHA',
        'custom_css_js' => 'Custom CSS/JS',
        'facebook' => 'Facebook',
        'google' => 'Google',
        'free_shipping' => 'Free Shipping',
        'local_pickup' => 'Local Pickup',
        'flat_rate' => 'Flat Rate',
        'flat_rate_2' => 'Flat Rate',
        'paypal' => 'PayPal',
        'stripe' => 'Stripe',
        'paytm' => 'Paytm',
        'razorpay' => 'Razorpay',
        'instamojo' => 'Instamojo',
        'paystack' => 'Paystack',
        'authorizenet' => 'Authorize.net',
        'mercadopago' => 'Mercado Pago',
        'flutterwave' => 'Flutterwave',
        'iyzico' => 'Iyzico',
        'payfast' => 'PayFast',
        'cod' => 'Cash On Delivery',
        'bank_transfer' => 'Bank Transfer',
        'check_payment' => 'Check / Money Order',
    ],
    'form' => [
        'logo' => 'Logo',
        'small_logo' => 'Small Logo',
        'enable_pwa' => 'Enable PWA',
        'pwa_icon' => 'Icon',
        'pwa_displays' => [
            'fullscreen' => 'Fullscreen',
            'standalone' => 'Standalone',
            'minimal-ui' => 'Minimal-UI',
            'browser' => 'Browser',
        ],
        'pwa_orientations' => [
            'any' => 'Any',
            'natural' => 'Natural',
            'landscape' => 'Landscape',
            'portrait' => 'Portrait',
            'portrait-primary' => 'Portrait-Primary',
            'portrait-secondary' => 'Portrait-Secondary',
            'landscape-primary' => 'Landscape-Primary',
            'landscape-secondary' => 'Landscape-Secondary',
        ],
        'pwa_directions' => [
            'ltr' => 'LTR',
            'rtl' => 'RTL',
            'auto' => 'Auto',
        ],
        'allow_reviews' => 'Allow customers to give reviews & ratings',
        'approve_reviews_automatically' => 'Customer reviews will be approved automatically',
        'show_cookie_bar' => 'Show cookie bar in your website',
        'privacy_settings' => 'Privacy Settings',
        'hide_store_phone' => 'Hide store phone from the storefront',
        'hide_store_email' => 'Hide store email from the storefront',
        'put_the_application_into_maintenance_mode' => 'Put the application into maintenance mode',
        'ip_addreses_seperated_in_new_line' => 'IP addreses seperated in new line',
        'select_service' => 'Select Service',
        'enable_auto_refreshing_currency_rates' => 'Enable auto-refreshing currency rates',
        'auto_refresh_currency_rate_frequencies' => [
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
        ],
        'customer_notification_settings' => 'Customer Notification Settings',
        'send_welcome_sms_after_registration' => 'Send welcome SMS after registration',
        'order_notification_settings' => 'Order Notification Settings',
        'send_new_order_notification_to_customer' => 'Send new order notification to the customer',
        'send_new_order_notification_to_admin' => 'Send new order notification to the admin',
        'mail_encryption_protocols' => [
            'ssl' => 'SSL',
            'tls' => 'Tls',
        ],
        'send_welcome_email_after_registration' => 'Send welcome email after registration',
        'send_invoice_email' => 'Send invoice email to the customer after checkout',
        'allow_customers_to_subscribe' => 'Allow customers to subscribe to your newsletter',
        'enable_google_recaptcha' => 'Enable Google reCAPTCHA (V2) in Registration, Review and Contact Us Forms',
        'enable_facebook_login' => 'Enable Facebook Login',
        'enable_google_login' => 'Enable Google Login',
        'enable_free_shipping' => 'Enable Free Shipping',
        'enable_local_pickup' => 'Enable Local Pickup',
        'enable_flat_rate' => 'Enable Flat Rate',
        'enable_flat_rate_2' => 'Enable Flat Rate 2',
        'enable_paypal' => 'Enable PayPal',
        'use_sandbox_for_test_payments' => 'Use sandbox for test payments',
        'enable_stripe' => 'Enable Stripe',
        'enable_paytm' => 'Enable Paytm',
        'enable_razorpay' => 'Enable Razorpay',
        'enable_instamojo' => 'Enable Instamojo',
        'enable_paystack' => 'Enable Paystack',
        'enable_authorizenet' => 'Enable Authorize.net',
        'enable_mercadopago' => 'Enable Mercado Pago',
        'enable_flutterwave' => 'Enable Flutterwave',
        'enable_iyzico' => 'Enable Iyzico',
        'enable_payfast' => 'Enable PayFast',
        'enable_cod' => 'Enable Cash On Delivery',
        'enable_bank_transfer' => 'Enable Bank Transfer',
        'enable_check_payment' => 'Enable Check / Money Order',
    ],
    'validation' => [
        'sqlite_is_not_installed' => 'SQLite is not installed',
    ],
];
