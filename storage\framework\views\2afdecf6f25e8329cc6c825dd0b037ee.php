<?php $__env->startComponent('admin::components.page.header'); ?>
    <?php $__env->slot('title', trans('report::admin.reports')); ?>

    <li class="active"><?php echo e(trans('report::admin.reports')); ?></li>
<?php echo $__env->renderComponent(); ?>

<?php $__env->startSection('content'); ?>
    <div class="report-wrapper">
        <div class="row">
            <div class="col-lg-9 col-md-8">
                <div class="report-result box">
                    <?php echo $__env->yieldContent('report_result'); ?>
                </div>
            </div>

            <div class="col-lg-3 col-md-4">
                <div class="filter-report box">
                    <div class="box-header">
                        <h5 class="tab-content-title"><?php echo e(trans('report::admin.filter')); ?></h5>
                    </div>

                    <div class="box-body">
                        <form method="GET" action="<?php echo e(route('admin.reports.index')); ?>">
                            <div class="form-group">
                                <label for="report-type"><?php echo e(trans('report::admin.filters.report_type')); ?></label>

                                <select name="type" id="report-type" class="custom-select-black">
                                    <?php $__currentLoopData = trans('report::admin.filters.report_types'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($type); ?>" <?php echo e($request->type === $type ? 'selected' : ''); ?>>
                                            <?php echo e($label); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <?php echo $__env->yieldContent('filters'); ?>

                            <button type="submit" class="btn btn-default" data-loading>
                                <?php echo e(trans('report::admin.filter')); ?>

                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('globals'); ?>
    <?php echo app('Illuminate\Foundation\Vite')([
        'modules/Report/Resources/assets/admin/sass/main.scss',
        'modules/Report/Resources/assets/admin/js/main.js'
    ]); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin::layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Documents\WORK\drRamiwork\app (1)\modules/Report/Resources/views/admin/reports/layout.blade.php ENDPATH**/ ?>