a:3:{i:0;a:6:{s:8:"settings";s:8:"Settings";s:4:"tabs";a:32:{s:5:"group";a:4:{s:16:"general_settings";s:16:"General Settings";s:13:"social_logins";s:13:"Social Logins";s:16:"shipping_methods";s:16:"Shipping Methods";s:15:"payment_methods";s:15:"Payment Methods";}s:7:"general";s:7:"General";s:4:"logo";s:4:"Logo";s:11:"maintenance";s:11:"Maintenance";s:5:"store";s:5:"Store";s:3:"pwa";s:3:"PWA";s:8:"currency";s:8:"Currency";s:3:"sms";s:3:"SMS";s:4:"mail";s:4:"Mail";s:10:"newsletter";s:10:"Newsletter";s:16:"google_recaptcha";s:16:"Google reCAPTCHA";s:13:"custom_css_js";s:13:"Custom CSS/JS";s:8:"facebook";s:8:"Facebook";s:6:"google";s:6:"Google";s:13:"free_shipping";s:13:"Free Shipping";s:12:"local_pickup";s:12:"Local Pickup";s:9:"flat_rate";s:9:"Flat Rate";s:11:"flat_rate_2";N;s:6:"paypal";s:6:"PayPal";s:6:"stripe";s:6:"Stripe";s:5:"paytm";s:5:"Paytm";s:8:"razorpay";s:8:"Razorpay";s:9:"instamojo";s:9:"Instamojo";s:8:"paystack";s:8:"Paystack";s:12:"authorizenet";s:13:"Authorize.net";s:11:"mercadopago";s:12:"Mercado Pago";s:11:"flutterwave";s:11:"Flutterwave";s:6:"iyzico";s:6:"Iyzico";s:7:"payfast";s:7:"PayFast";s:3:"cod";s:16:"Cash On Delivery";s:13:"bank_transfer";s:13:"Bank Transfer";s:13:"check_payment";s:19:"Check / Money Order";}s:4:"form";a:49:{s:4:"logo";s:4:"Logo";s:10:"small_logo";s:10:"Small Logo";s:10:"enable_pwa";s:10:"Enable PWA";s:8:"pwa_icon";s:4:"Icon";s:12:"pwa_displays";a:4:{s:10:"fullscreen";s:10:"Fullscreen";s:10:"standalone";s:10:"Standalone";s:10:"minimal-ui";s:10:"Minimal-UI";s:7:"browser";s:7:"Browser";}s:16:"pwa_orientations";a:8:{s:3:"any";s:3:"Any";s:7:"natural";s:7:"Natural";s:9:"landscape";s:9:"Landscape";s:8:"portrait";s:8:"Portrait";s:16:"portrait-primary";s:16:"Portrait-Primary";s:18:"portrait-secondary";s:18:"Portrait-Secondary";s:17:"landscape-primary";s:17:"Landscape-Primary";s:19:"landscape-secondary";s:19:"Landscape-Secondary";}s:14:"pwa_directions";a:3:{s:3:"ltr";s:3:"LTR";s:3:"rtl";s:3:"RTL";s:4:"auto";s:4:"Auto";}s:13:"allow_reviews";s:41:"Allow customers to give reviews & ratings";s:29:"approve_reviews_automatically";s:47:"Customer reviews will be approved automatically";s:15:"show_cookie_bar";s:31:"Show cookie bar in your website";s:16:"privacy_settings";s:16:"Privacy Settings";s:16:"hide_store_phone";s:36:"Hide store phone from the storefront";s:16:"hide_store_email";s:36:"Hide store email from the storefront";s:41:"put_the_application_into_maintenance_mode";s:41:"Put the application into maintenance mode";s:33:"ip_addreses_seperated_in_new_line";s:33:"IP addreses seperated in new line";s:14:"select_service";s:14:"Select Service";s:37:"enable_auto_refreshing_currency_rates";s:37:"Enable auto-refreshing currency rates";s:38:"auto_refresh_currency_rate_frequencies";a:3:{s:5:"daily";s:5:"Daily";s:6:"weekly";s:6:"Weekly";s:7:"monthly";s:7:"Monthly";}s:30:"customer_notification_settings";s:30:"Customer Notification Settings";s:35:"send_welcome_sms_after_registration";s:35:"Send welcome SMS after registration";s:27:"order_notification_settings";s:27:"Order Notification Settings";s:39:"send_new_order_notification_to_customer";s:43:"Send new order notification to the customer";s:36:"send_new_order_notification_to_admin";s:40:"Send new order notification to the admin";s:25:"mail_encryption_protocols";a:2:{s:3:"ssl";s:3:"SSL";s:3:"tls";s:3:"Tls";}s:37:"send_welcome_email_after_registration";s:37:"Send welcome email after registration";s:18:"send_invoice_email";s:49:"Send invoice email to the customer after checkout";s:28:"allow_customers_to_subscribe";s:47:"Allow customers to subscribe to your newsletter";s:23:"enable_google_recaptcha";s:73:"Enable Google reCAPTCHA (V2) in Registration, Review and Contact Us Forms";s:21:"enable_facebook_login";s:21:"Enable Facebook Login";s:19:"enable_google_login";s:19:"Enable Google Login";s:20:"enable_free_shipping";s:20:"Enable Free Shipping";s:19:"enable_local_pickup";s:19:"Enable Local Pickup";s:16:"enable_flat_rate";s:16:"Enable Flat Rate";s:18:"enable_flat_rate_2";s:16:"Enable Flat Rate";s:13:"enable_paypal";s:13:"Enable PayPal";s:29:"use_sandbox_for_test_payments";s:29:"Use sandbox for test payments";s:13:"enable_stripe";s:13:"Enable Stripe";s:12:"enable_paytm";s:12:"Enable Paytm";s:15:"enable_razorpay";s:15:"Enable Razorpay";s:16:"enable_instamojo";s:16:"Enable Instamojo";s:15:"enable_paystack";s:15:"Enable Paystack";s:19:"enable_authorizenet";s:20:"Enable Authorize.net";s:18:"enable_mercadopago";s:19:"Enable Mercado Pago";s:18:"enable_flutterwave";s:18:"Enable Flutterwave";s:13:"enable_iyzico";s:13:"Enable Iyzico";s:14:"enable_payfast";s:14:"Enable PayFast";s:10:"enable_cod";s:23:"Enable Cash On Delivery";s:20:"enable_bank_transfer";s:20:"Enable Bank Transfer";s:20:"enable_check_payment";s:26:"Enable Check / Money Order";}s:10:"validation";a:1:{s:23:"sqlite_is_not_installed";s:23:"SQLite is not installed";}s:11:"store_phone";N;s:16:"store_phone_hide";N;}i:1;a:1:{s:29:"_fleetcart_cache_translations";s:29:"_fleetcart_cache_translations";}i:2;i:**********;}